(self.webpackChunk_canva_web=self.webpackChunk_canva_web||[]).push([[398821],{414069:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(824166);class n{static __initStatic(){this.id="RewriteFrames"}__init(){this.name=n.id}__init2(){this._prefix="app:///"}constructor(e={}){n.prototype.__init.call(this),n.prototype.__init2.call(this),n.prototype.__init3.call(this),e.root&&(this._root=e.root),e.prefix&&(this._prefix=e.prefix),e.iteratee&&(this._iteratee=e.iteratee)}setupOnce(e,t){e((e=>{const r=t().getIntegration(n);return r?r.process(e):e}))}process(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=this._processExceptionsEvent(t)),t}__init3(){this._iteratee=e=>{if(!e.filename)return e;const t=/^[A-Z]:\\/.test(e.filename),r=/^\//.test(e.filename);if(t||r){const r=t?e.filename.replace(/^[A-Z]:/,"").replace(/\\/g,"/"):e.filename,n=this._root?(0,s.V8)(this._root,r):(0,s.P8)(r);e.filename=`${this._prefix}${n}`}return e}}_processExceptionsEvent(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map((e=>({...e,...e.stacktrace&&{stacktrace:this._processStacktrace(e.stacktrace)}})))}}}catch(t){return e}}_processStacktrace(e){return{...e,frames:e&&e.frames&&e.frames.map((e=>this._iteratee(e)))}}}n.__initStatic()},588587:(e,t,r)=>{"use strict";var s,n=r(776266);e=r.hmd(e),s="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==r.g?r.g:e;(0,n.A)(s)},613444:(e,t,r)=>{const{enabledForCurrentPage:s}=r(298510);e.exports=r(s?31968:589159)},776266:(e,t,r)=>{"use strict";function s(e){var t,r=e.Symbol;if("function"==typeof r)if(r.observable)t=r.observable;else{t="function"==typeof r.for?r.for("https://github.com/benlesh/symbol-observable"):r("https://github.com/benlesh/symbol-observable");try{r.observable=t}catch(s){}}else t="@@observable";return t}r.d(t,{A:()=>s})},824166:(e,t,r)=>{"use strict";function s(e,t){let r=0;for(let s=e.length-1;s>=0;s--){const t=e[s];"."===t?e.splice(s,1):".."===t?(e.splice(s,1),r++):r&&(e.splice(s,1),r--)}if(t)for(;r--;r)e.unshift("..");return e}r.d(t,{P8:()=>l,V8:()=>a});const n=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^/]+?|)(\.[^./]*|))(?:[/]*)$/;function i(e){const t=n.exec(e);return t?t.slice(1):[]}function c(...e){let t="",r=!1;for(let s=e.length-1;s>=-1&&!r;s--){const n=s>=0?e[s]:"/";n&&(t=`${n}/${t}`,r="/"===n.charAt(0))}return t=s(t.split("/").filter((e=>!!e)),!r).join("/"),(r?"/":"")+t||"."}function o(e){let t=0;for(;t<e.length&&""===e[t];t++);let r=e.length-1;for(;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}function a(e,t){e=c(e).substr(1),t=c(t).substr(1);const r=o(e.split("/")),s=o(t.split("/")),n=Math.min(r.length,s.length);let i=n;for(let c=0;c<n;c++)if(r[c]!==s[c]){i=c;break}let a=[];for(let c=i;c<r.length;c++)a.push("..");return a=a.concat(s.slice(i)),a.join("/")}function l(e,t){let r=i(e)[2];return t&&r.substr(-1*t.length)===t&&(r=r.substr(0,r.length-t.length)),r}},882262:e=>{!function(){function t(e,t){var r,s,n,i,c,o,a,l;for(r=3&e.length,s=e.length-r,n=t,c=3432918353,o=461845907,l=0;l<s;)a=255&e.charCodeAt(l)|(255&e.charCodeAt(++l))<<8|(255&e.charCodeAt(++l))<<16|(255&e.charCodeAt(++l))<<24,++l,n=27492+(65535&(i=5*(65535&(n=(n^=a=(65535&(a=(a=(65535&a)*c+(((a>>>16)*c&65535)<<16)&4294967295)<<15|a>>>17))*o+(((a>>>16)*o&65535)<<16)&4294967295)<<13|n>>>19))+((5*(n>>>16)&65535)<<16)&4294967295))+((58964+(i>>>16)&65535)<<16);switch(a=0,r){case 3:a^=(255&e.charCodeAt(l+2))<<16;case 2:a^=(255&e.charCodeAt(l+1))<<8;case 1:n^=a=(65535&(a=(a=(65535&(a^=255&e.charCodeAt(l)))*c+(((a>>>16)*c&65535)<<16)&4294967295)<<15|a>>>17))*o+(((a>>>16)*o&65535)<<16)&4294967295}return n^=e.length,n=2246822507*(65535&(n^=n>>>16))+((2246822507*(n>>>16)&65535)<<16)&4294967295,n=3266489909*(65535&(n^=n>>>13))+((3266489909*(n>>>16)&65535)<<16)&4294967295,(n^=n>>>16)>>>0}var r=t;r.v2=function(e,t){for(var r,s=e.length,n=t^s,i=0;s>=4;)r=1540483477*(65535&(r=255&e.charCodeAt(i)|(255&e.charCodeAt(++i))<<8|(255&e.charCodeAt(++i))<<16|(255&e.charCodeAt(++i))<<24))+((1540483477*(r>>>16)&65535)<<16),n=1540483477*(65535&n)+((1540483477*(n>>>16)&65535)<<16)^(r=1540483477*(65535&(r^=r>>>24))+((1540483477*(r>>>16)&65535)<<16)),s-=4,++i;switch(s){case 3:n^=(255&e.charCodeAt(i+2))<<16;case 2:n^=(255&e.charCodeAt(i+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(i)))+((1540483477*(n>>>16)&65535)<<16)}return n=1540483477*(65535&(n^=n>>>13))+((1540483477*(n>>>16)&65535)<<16),(n^=n>>>15)>>>0},r.v3=t,e.exports=r}()}}]);
//# sourceMappingURL=sourcemaps/f7ff214bc402678f.vendor.js.map