/**
* Copyright (c) 2017-present, Facebook, Inc. All rights reserved.
*
* You are hereby granted a non-exclusive, worldwide, royalty-free license to use,
* copy, modify, and distribute this software in source code or binary form for use
* in connection with the web services and APIs provided by Facebook.
*
* As with any software that integrates with the Facebook platform, your use of
* this software is subject to the Facebook Platform Policy
* [http://developers.facebook.com/policy/]. This copyright notice shall be
* included in all copies or substantial portions of the software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
* FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
* COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
* IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
* CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("signalsFBEventsGetIwlUrl",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var b=f.getFbeventsModules("signalsFBEventsGetTier"),c=d();function d(){try{if(a.trustedTypes&&a.trustedTypes.createPolicy){var b=a.trustedTypes;return b.createPolicy("facebook.com/signals/iwl",{createScriptURL:function(b){var c=typeof a.URL==="function"?a.URL:a.webkitURL;c=new c(b);c=c.hostname.endsWith(".facebook.com")&&c.pathname=="/signals/iwl.js";if(!c)throw new Error("Disallowed script URL");return b}})}}catch(a){}return null}e.exports=function(a,d,e){d=b(d);d=d==null?"www.facebook.com":"www."+d+".facebook.com";d="https://"+d+"/signals/iwl.js?pixel_id="+a+"&access_token="+e;if(c!=null)return c.createScriptURL(d);else return d}})();return e.exports}(a,b,c,d)});
f.ensureModuleRegistered("signalsFBEventsGetTier",function(){
return function(f,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=/^https:\/\/www\.([A-Za-z0-9\.]+)\.facebook\.com\/tr\/?$/,b=["https://www.facebook.com/tr","https://www.facebook.com/tr/"];e.exports=function(c){if(b.indexOf(c)!==-1)return null;var d=a.exec(c);if(d==null)throw new Error("Malformed tier: "+c);return d[1]}})();return e.exports}(a,b,c,d)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.iwlbootstrapper",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var c=f.getFbeventsModules("SignalsFBEventsIWLBootStrapEvent"),d=f.getFbeventsModules("SignalsFBEventsLogging"),g=f.getFbeventsModules("SignalsFBEventsNetworkConfig"),h=f.getFbeventsModules("SignalsFBEventsPlugin"),i=f.getFbeventsModules("signalsFBEventsGetIwlUrl"),j=f.getFbeventsModules("signalsFBEventsGetTier"),k=d.logUserError,l=/^https:\/\/.*\.facebook\.com$/i,m="FACEBOOK_IWL_CONFIG_STORAGE_KEY",n=null;e.exports=new h(function(d,e){try{n=a.sessionStorage?a.sessionStorage:{getItem:function(a){return null},removeItem:function(a){},setItem:function(a,b){}}}catch(a){return}function h(c,d,e){var f=b.createElement("script");f.async=!0;f.onload=function(){if(!a.FacebookIWL||!a.FacebookIWL.init)return;var b=j(g.ENDPOINT);b!=null&&a.FacebookIWL.set&&a.FacebookIWL.set("tier",b);e()};a.FacebookIWLSessionEnd=function(){n.removeItem(m),a.close()};f.src=i(c,g.ENDPOINT,d);b.body&&b.body.appendChild(f)}var o=!1,p=function(a){return!!(e&&e.pixelsByID&&Object.prototype.hasOwnProperty.call(e.pixelsByID,a))};function q(){if(o)return;var b=n.getItem(m);if(!b)return;b=JSON.parse(b);var c=b.pixelID,d=b.graphToken,e=b.sessionStartTime;o=!0;h(c,d,function(){var b=p(c)?c.toString():null;a.FacebookIWL.init(b,d,e)})}function r(b,c){if(o)return;h(b,c,function(){return a.FacebookIWL.showConfirmModal(b)})}function s(a,b,c){n.setItem(m,JSON.stringify({graphToken:a,pixelID:b,sessionStartTime:c})),q()}c.listen(function(b){var c=b.graphToken;b=b.pixelID;s(c,b);a.FacebookIWLSessionEnd=function(){return n.removeItem(m)}});function d(a){var b=a.data,c=b.graphToken,d=b.msg_type,f=b.pixelID;b=b.sessionStartTime;if(e&&e.pixelsByID&&e.pixelsByID[f]&&e.pixelsByID[f].codeless==="false"){k({pixelID:f,type:"SITE_CODELESS_OPT_OUT"});return}if(n.getItem(m)||!l.test(a.origin)||!(a.data&&(d==="FACEBOOK_IWL_BOOTSTRAP"||d==="FACEBOOK_IWL_CONFIRM_DOMAIN")))return;if(!Object.prototype.hasOwnProperty.call(e.pixelsByID,f)){a.source.postMessage("FACEBOOK_IWL_ERROR_PIXEL_DOES_NOT_MATCH",a.origin);return}switch(d){case"FACEBOOK_IWL_BOOTSTRAP":a.source.postMessage("FACEBOOK_IWL_BOOTSTRAP_ACK",a.origin);s(c,f,b);break;case"FACEBOOK_IWL_CONFIRM_DOMAIN":a.source.postMessage("FACEBOOK_IWL_CONFIRM_DOMAIN_ACK",a.origin);r(f,c);break}}if(n.getItem(m)){q();return}a.opener&&a.addEventListener("message",d)})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.iwlbootstrapper");f.registerPlugin&&f.registerPlugin("fbevents.plugins.iwlbootstrapper",e.exports);
f.ensureModuleRegistered("fbevents.plugins.iwlbootstrapper",function(){
return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("sha256_with_dependencies_new",function(){
return function(f,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";function a(a){var b="",c=void 0,d;for(var e=0;e<a.length;e++)c=a.charCodeAt(e),d=e+1<a.length?a.charCodeAt(e+1):0,c>=55296&&c<=56319&&d>=56320&&d<=57343&&(c=65536+((c&1023)<<10)+(d&1023),e++),c<=127?b+=String.fromCharCode(c):c<=2047?b+=String.fromCharCode(192|c>>>6&31,128|c&63):c<=65535?b+=String.fromCharCode(224|c>>>12&15,128|c>>>6&63,128|c&63):c<=2097151&&(b+=String.fromCharCode(240|c>>>18&7,128|c>>>12&63,128|c>>>6&63,128|c&63));return b}function b(a,b){return b>>>a|b<<32-a}function c(a,b,c){return a&b^~a&c}function d(a,b,c){return a&b^a&c^b&c}function f(a){return b(2,a)^b(13,a)^b(22,a)}function g(a){return b(6,a)^b(11,a)^b(25,a)}function h(a){return b(7,a)^b(18,a)^a>>>3}function i(a){return b(17,a)^b(19,a)^a>>>10}function j(a,b){return a[b&15]+=i(a[b+14&15])+a[b+9&15]+h(a[b+1&15])}var k=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],l=new Array(8),m=new Array(2),n=new Array(64),o=new Array(16),p="0123456789abcdef";function q(a,b){var c=(a&65535)+(b&65535);a=(a>>16)+(b>>16)+(c>>16);return a<<16|c&65535}function r(){m[0]=m[1]=0,l[0]=1779033703,l[1]=3144134277,l[2]=1013904242,l[3]=2773480762,l[4]=1359893119,l[5]=2600822924,l[6]=528734635,l[7]=1541459225}function s(){var a=void 0,b=void 0,e=void 0,h=void 0,i=void 0,m=void 0,p=void 0,r=void 0,s=void 0,t=void 0;e=l[0];h=l[1];i=l[2];m=l[3];p=l[4];r=l[5];s=l[6];t=l[7];for(var u=0;u<16;u++)o[u]=n[(u<<2)+3]|n[(u<<2)+2]<<8|n[(u<<2)+1]<<16|n[u<<2]<<24;for(u=0;u<64;u++)a=t+g(p)+c(p,r,s)+k[u],u<16?a+=o[u]:a+=j(o,u),b=f(e)+d(e,h,i),t=s,s=r,r=p,p=q(m,a),m=i,i=h,h=e,e=q(a,b);l[0]+=e;l[1]+=h;l[2]+=i;l[3]+=m;l[4]+=p;l[5]+=r;l[6]+=s;l[7]+=t}function t(a,b){var c=void 0,d,e=0;d=m[0]>>3&63;var f=b&63;(m[0]+=b<<3)<b<<3&&m[1]++;m[1]+=b>>29;for(c=0;c+63<b;c+=64){for(var g=d;g<64;g++)n[g]=a.charCodeAt(e++);s();d=0}for(g=0;g<f;g++)n[g]=a.charCodeAt(e++)}function u(){var a=m[0]>>3&63;n[a++]=128;if(a<=56)for(var b=a;b<56;b++)n[b]=0;else{for(b=a;b<64;b++)n[b]=0;s();for(a=0;a<56;a++)n[a]=0}n[56]=m[1]>>>24&255;n[57]=m[1]>>>16&255;n[58]=m[1]>>>8&255;n[59]=m[1]&255;n[60]=m[0]>>>24&255;n[61]=m[0]>>>16&255;n[62]=m[0]>>>8&255;n[63]=m[0]&255;s()}function v(){var a="";for(var b=0;b<8;b++)for(var c=28;c>=0;c-=4)a+=p.charAt(l[b]>>>c&15);return a}function w(a){var b=0;for(var c=0;c<8;c++)for(var d=28;d>=0;d-=4)a[b++]=p.charCodeAt(l[c]>>>d&15)}function x(a,b){r();t(a,a.length);u();if(b)w(b);else return v()}function y(b){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,d=arguments[2];if(b===null||b===void 0)return null;var e=b;c&&(e=a(b));return x(e,d)}e.exports=y})();return e.exports}(a,b,c,d)});
f.ensureModuleRegistered("signalsFBEventsExtractMicrodataSchemas",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var b=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a},c=f.getFbeventsModules("SignalsFBEventsGuardrail"),d=f.getFbeventsModules("SignalsFBEventsShared");d=d.MicrodataExtractionMethods;var g=d.extractJsonLd,h=d.extractMeta,i=d.extractOpenGraph,j=d.extractSchemaOrg,k=d.mergeProductMetadata,l=f.getFbeventsModules("SignalsFBEventsQE");d=f.getFbeventsModules("SignalsFBEventsUtils");var m=d.keys;d=f.getFbeventsModules("SignalsFBEventsLogging");var n=d.logError,o=d.logUserError,p=d.logInfoString,q=f.getFbeventsModules("sha256_with_dependencies_new"),r="pixel",s="extractMicrodataSchemas",t=f.getFbeventsModules("SignalsFBEventsQE");d=f.getFbeventsModules("SignalsFBEventsExperimentNames");var u=d.AUTOMATIC_PARAMETERS_QUALITY;function v(b,a){if(!c.eval("enable_automatic_parameter_logging")||Math.random()>.05)return;p(b,"automatic_parameters",a)}function w(c){c.id;var d=c.includeJsonLd;d=d===void 0?!1:d;c.instance;var e=c.onlyHash;e=e===void 0?!1:e;var f=c.includeAutomaticParameters;f=f===void 0?!1:f;c=c.includePdpData;c=c===void 0?!1:c;var h=t.isInTest(u),p={automaticParameters:{},productID:null,productUrl:null,pdpData:{}},w={extractedProperties:{},productMetadata:p};try{w=i(f,h)}catch(a){var y="[Microdata OpenGraph]";a!=null&&a.message!=null&&(y+=": "+a.message);n(new Error(y),r,s)}y=w.extractedProperties;var z={extractedProperties:[],invalidInnerTexts:[],productMetadata:p};try{z=d?g(f,h,v):{extractedProperties:[],invalidInnerTexts:[],productMetadata:p}}catch(a){var A="[Microdata JSON LD]";a!=null&&a.message!=null&&(A+=": "+a.message);n(new Error(A),r,s)}A=z.extractedProperties;for(var B=0;B<z.invalidInnerTexts.length;B++)o({jsonLd:z.invalidInnerTexts[B],type:"INVALID_JSON_LD"});B=x();var C={extractedProperties:[],productMetadata:p};try{C=j(f,h)}catch(a){h="[Microdata Schema]";a!=null&&a.message!=null&&(h+=": "+a.message);n(new Error(h),r,s)}h=C.extractedProperties;p=k([w.productMetadata,z.productMetadata,C.productMetadata]);w=l.get("logDataLayer");z=w&&w.isInExperimentGroup;C=z===!0?a.dataLayer||[]:[];if(h.length>0||A.length>0||m(y).length>0||m(B).length>1||B.title!==""||C.length&&C.length>0){w={DataLayer:C,Meta:B,OpenGraph:y,"Schema.org":h};d&&(w=b({},w,{"JSON-LD":A}));z=q(JSON.stringify(w));z!=null&&(z=z.substring(0,24));if(e)return{hmd:z,pid:p.productID,pl:p.productUrl};if(f)return{ap:p.automaticParameters};return c?{pdp:p.pdpData}:w}}function x(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,b={title:""};try{b=h(a)}catch(a){var c="[Microdata Metadata]";a!=null&&a.message!=null&&(c+=": "+a.message);n(new Error(c),r,s)}return b}e.exports={extractAllSchemas:w,extractMetaWithErrorLogging:x}})();return e.exports}(a,b,c,d)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.microdata",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var b=f.getFbeventsModules("SignalsFBEventsPlugin"),c=f.getFbeventsModules("SignalsFBEventsUtils"),d=c.some;c=f.getFbeventsModules("SignalsFBEventsEvents");c.fired;var g=c.getCustomParameters;c=f.getFbeventsModules("SignalsFBEventsLogging");c.logError;f.getFbeventsModules("sha256_with_dependencies_new");var h=f.getFbeventsModules("SignalsFBEventsConfigStore");c=f.getFbeventsModules("signalsFBEventsExtractMicrodataSchemas");var i=c.extractAllSchemas,j=null,k=null;e.exports=new b(function(b,c){b=a.performance!=null&&a.performance.timing!=null&&a.performance.timing.loadEventEnd!=null?a.performance.timing.loadEventEnd:Date.now();b!==0?b:Date.now();({});g.listen(function(a,b){if(c.disableAutoConfig||b!=="PageView"&&b!=="Microdata")return{};b=d(c.getOptedInPixels("Microdata"),function(b){return b.id===a.id});if(!b)return{};b=h.get(a.id,"microdata");if(b!=null&&b.enablePageHash===!0){if(j==null){b=d(c.getOptedInPixels("MicrodataJsonLd"),function(b){return b.id===a.id});k=i({id:a.id,includeJsonLd:b,instance:c,onlyHash:!0});k!=null&&(j=String(k.hmd))}if(j!=null)return k}return{}})})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.microdata");f.registerPlugin&&f.registerPlugin("fbevents.plugins.microdata",e.exports);
f.ensureModuleRegistered("fbevents.plugins.microdata",function(){
return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.unwanteddata",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsEvents");a.configLoaded;var b=a.validateCustomParameters,c=a.validateUrlParameters,d=f.getFbeventsModules("SignalsFBEventsConfigStore"),g=f.getFbeventsModules("SignalsFBEventsLogging");a=f.getFbeventsModules("SignalsFBEventsPlugin");var h=f.getFbeventsModules("SignalsFBEventsUtils"),i=f.getFbeventsModules("sha256_with_dependencies_new");h.each;var j=h.map,k=!1;f.getFbeventsModules("SignalsParamList");e.exports=new a(function(a,e){b.listen(function(b,c,f){if(b==null)return{};a.performanceMark("fbevents:start:unwantedDataProcessing",b.id);var h=e.optIns.isOptedIn(b.id,"UnwantedData");if(!h)return{};h=e.optIns.isOptedIn(b.id,"ProtectedDataMode");var k=d.get(b.id,"unwantedData");if(k==null)return{};var l=!1,m=[],n=[],o={};if(k.blacklisted_keys!=null){var p=k.blacklisted_keys[f];if(p!=null){p=p.cd;j(p,function(a){Object.prototype.hasOwnProperty.call(c,a)&&(l=!0,m.push(a),delete c[a])})}}if(k.sensitive_keys!=null){p=k.sensitive_keys[f];if(p!=null){var q=p.cd;Object.keys(c).forEach(function(a){j(q,function(b){i(a)===b&&(l=!0,n.push(b),delete c[a])})})}}o.unwantedParams=m;o.restrictedParams=n;if(l&&!h){k=m.length>0;f=n.length>0;if(k||f){a.performanceMark("fbevents:end:unwantedDataProcessing",b.id);g.logUserError({type:"UNWANTED_CUSTOM_DATA"});p={};k&&(p.up=m.join(","));f&&(p.rp=n.join(","));return p}}a.performanceMark("fbevents:end:unwantedDataProcessing",b.id);return{}});function h(a,b,c,d,e){var f=new URLSearchParams(b.search),g=[],h=[];b={};if(c.blacklisted_keys!=null){var l=c.blacklisted_keys[d];if(l!=null){l=l.url;j(l,function(a){f.has(a)&&(k=!0,g.push(a),f.set(a,"_removed_"))})}}if(c.sensitive_keys!=null){l=c.sensitive_keys[d];if(l!=null){var m=l.url;f.forEach(function(a,b){j(m,function(a){i(b)===a&&(k=!0,h.push(a),f.set(b,"_removed_"))})})}}b.unwantedParams=g;b.restrictedParams=h;if(k){e||(g.length>0&&a.append("up_url",g.join(",")),h.length>0&&a.append("rp_url",h.join(",")));return f.toString()}return""}c.listen(function(b,c,f,i){if(b==null)return;a.performanceMark("fbevents:start:validateUrlProcessing",b.id);var j=e.optIns.isOptedIn(b.id,"UnwantedData");if(!j)return;j=e.optIns.isOptedIn(b.id,"ProtectedDataMode");var l=d.get(b.id,"unwantedData");if(l==null)return;k=!1;if(Object.prototype.hasOwnProperty.call(c,"dl")&&c.dl.length>0){var m=new URL(c.dl),n=h(i,m,l,f,j);k&&n.length>0&&(m.search=n,c.dl=m.toString())}if(Object.prototype.hasOwnProperty.call(c,"rl")&&c.rl.length>0){n=new URL(c.rl);m=h(i,n,l,f,j);k&&m.length>0&&(n.search=m,c.rl=n.toString())}k&&g.logUserError({type:"UNWANTED_URL_DATA"});a.performanceMark("fbevents:end:validateUrlProcessing",b.id)})})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.unwanteddata");f.registerPlugin&&f.registerPlugin("fbevents.plugins.unwanteddata",e.exports);
f.ensureModuleRegistered("fbevents.plugins.unwanteddata",function(){
return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.eventvalidation",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsPlugin"),b=f.getFbeventsModules("SignalsFBEventsSendEventEvent"),c=f.getFbeventsModules("SignalsFBEventsTyped"),d=c.coerce,g=c.Typed;c=f.getFbeventsModules("SignalsFBEventsLogging");var h=c.logUserError;e.exports=new a(function(a,c){b.listen(function(a){var b=a.id;a=a.eventName;b=d(b,g.fbid());if(b==null)return!1;var e=c.optIns.isOptedIn(b,"EventValidation");if(!e)return!1;e=c.pluginConfig.get(b,"eventValidation");if(e==null)return!1;b=e.unverifiedEventNames;e=e.restrictedEventNames;var f=!1,i=!1;b&&(f=b.includes(a),f&&h({type:"UNVERIFIED_EVENT"}));e&&(i=e.includes(a),i&&h({type:"RESTRICTED_EVENT"}));return f||i})})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.eventvalidation");f.registerPlugin&&f.registerPlugin("fbevents.plugins.eventvalidation",e.exports);
f.ensureModuleRegistered("fbevents.plugins.eventvalidation",function(){
return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("SignalsFBEventsClientHintTypedef",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsTyped");a.coerce;a=a.Typed;var b=a.objectWithFields({brands:a.array(),platform:a.allowNull(a.string()),getHighEntropyValues:a.func()});a=a.objectWithFields({model:a.allowNull(a.string()),platformVersion:a.allowNull(a.string()),fullVersionList:a.array()});e.exports={userAgentDataTypedef:b,highEntropyResultTypedef:a}})();return e.exports}(a,b,c,d)});
f.ensureModuleRegistered("SignalsFBEventsGetIsAndroidChrome",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("signalsFBEventsGetIsChrome");function b(a){return a===void 0?!1:a.platform==="Android"&&a.brands.map(function(a){return a.brand}).join(", ").includes("Chrome")}function c(a){return a.includes("Chrome")&&a.includes("Android")}function d(b){b=b.indexOf("Android")>=0;var c=a();return b&&c}e.exports={checkIsAndroidChromeWithClientHint:b,checkIsAndroidChromeWithUAString:c,checkIsAndroidChrome:d}})();return e.exports}(a,b,c,d)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.clienthint",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var b=f.getFbeventsModules("SignalsFBEventsEvents");b.fired;b=f.getFbeventsModules("SignalsFBEventsPlugin");var c=f.getFbeventsModules("SignalsParamList"),d=f.getFbeventsModules("signalsFBEventsSendEvent");d.sendEvent;d=f.getFbeventsModules("SignalsFBEventsEvents");d.configLoaded;f.getFbeventsModules("SignalsFBEventsSendEventEvent");d=f.getFbeventsModules("SignalsFBEventsLogging");d.logError;var g=d.logWarning,h=d.logInfo;d=f.getFbeventsModules("SignalsFBEventsTyped");var i=d.coerce;d.Typed;d=f.getFbeventsModules("SignalsFBEventsClientHintTypedef");var j=d.userAgentDataTypedef,k=d.highEntropyResultTypedef;d=f.getFbeventsModules("SignalsFBEventsGetIsAndroidChrome");var l=d.checkIsAndroidChrome,m="chmd",n="chpv",o="chfv",p=[m,n,o],q="clientHint",r="pixel",s="clienthint";function t(a){a=i(a,k);if(a==null){h(new Error("[ClientHint Error] getHighEntropyValues returned null from Android Chrome source"),r,s);return new Map()}var b=new Map();b.set(m,String(a.model));b.set(n,String(a.platformVersion));var c=void 0,d=void 0,e=!0,f=!1,g=void 0;try{for(var j=a.fullVersionList[typeof Symbol==="function"?Symbol.iterator:"@@iterator"](),a;!(e=(a=j.next()).done);e=!0)d=a.value,d.brand.includes("Chrome")&&(c=d.version)}catch(a){f=!0,g=a}finally{try{!e&&j["return"]&&j["return"]()}finally{if(f)throw g}}b.set(o,String(c));return b}function u(a,b){var c=!0,d=!1,e=void 0;try{for(var f=p[typeof Symbol==="function"?Symbol.iterator:"@@iterator"](),g;!(c=(g=f.next()).done);c=!0){g=g.value;a.get(g)==null&&a.append(g,b.get(g))}}catch(a){d=!0,e=a}finally{try{!c&&f["return"]&&f["return"]()}finally{if(d)throw e}}}function v(a,b,d){d=t(a);a=b.customParams||new c();u(a,d);b.customParams=a}e.exports=new b(function(b,c){b=i(a.navigator.userAgentData,j);if(b==null){a.navigator.userAgentData!=null&&g(new Error("[ClientHint Error] UserAgentData coerce error"));return}else if(!l(a.navigator.userAgent))return;b=a.navigator.userAgentData.getHighEntropyValues(["model","platformVersion","fullVersionList"]).then(function(a){var b=c.asyncParamFetchers.get(q);b!=null&&b.result==null&&(b.result=a,c.asyncParamFetchers.set(q,b));return a})["catch"](function(a){a.message="[ClientHint Error] Fetch error"+a.message,g(a)});c.asyncParamFetchers.set(q,{request:b,callback:v});c.asyncParamPromisesAllSettled=!1})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.clienthint");f.registerPlugin&&f.registerPlugin("fbevents.plugins.clienthint",e.exports);
f.ensureModuleRegistered("fbevents.plugins.clienthint",function(){
return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.unwantedparams",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsEvents"),b=a.validateCustomParameters,c=f.getFbeventsModules("SignalsFBEventsConfigStore");a=f.getFbeventsModules("SignalsFBEventsPlugin");f.getFbeventsModules("SignalsParamList");var d=f.getFbeventsModules("SignalsFBEventsUtils"),g=d.each;e.exports=new a(function(a,d){b.listen(function(b,e,f){if(b==null)return{};a.performanceMark("fbevents:start:unwantedParamsProcessing",b.id);f=d.optIns.isOptedIn(b.id,"UnwantedParams");if(!f)return{};f=c.get(b.id,"unwantedParams");if(f==null||f.unwantedParams==null)return{};g(f.unwantedParams,function(a){delete e[a]});a.performanceMark("fbevents:end:unwantedParamsProcessing",b.id);return{}})})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.unwantedparams");f.registerPlugin&&f.registerPlugin("fbevents.plugins.unwantedparams",e.exports);
f.ensureModuleRegistered("fbevents.plugins.unwantedparams",function(){
return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});
f.ensureModuleRegistered("SignalsFBEvents.plugins.standardparamchecks",function(){
return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsLogging"),b=a.logUserError;a=f.getFbeventsModules("SignalsFBEventsEvents");var c=a.lateValidateCustomParameters,d=f.getFbeventsModules("SignalsFBEventsConfigStore");a=f.getFbeventsModules("SignalsFBEventsPlugin");f.getFbeventsModules("SignalsParamList");var g=f.getFbeventsModules("SignalsFBEventsUtils"),h=g.each,i=g.some,j=g.keys;g.isNumber;function k(a,b){if(!b)return!1;return b.require_exact_match?i(b.potential_matches,function(b){return b.toLowerCase()===a.toLowerCase()}):i(b.potential_matches,function(b){return new RegExp(b).test(a)})}e.exports=new a(function(a,e){c.listen(function(a,c,f){f=e.optIns.isOptedIn(a,"StandardParamChecks");if(!f)return{};var g=d.get(a,"standardParamChecks");if(g==null||g.standardParamChecks==null)return{};var l=[];h(j(c),function(d){var e=g.standardParamChecks[d]||[];if(!e||e.length==0)return{};e=i(e,function(a){return k(String(c[d]),a)});e||(l.push(d),b({invalidParamName:d,pixelID:a,type:"INVALID_PARAM_FORMAT"}))});h(l,function(a){delete c[a]});return l.length>0?{rks:l.join(",")}:{}})})})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.standardparamchecks");f.registerPlugin&&f.registerPlugin("fbevents.plugins.standardparamchecks",e.exports);
f.ensureModuleRegistered("fbevents.plugins.standardparamchecks",function(){
return e.exports})})()})(window,document,location,history);