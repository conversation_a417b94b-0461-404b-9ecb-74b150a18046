(()=>{"use strict";var t={18830:(t,e,n)=>{n.d(e,{u:()=>o});var r=n(982745),s=n(978797);const i=4;function o(t){return(0,r.o)(t,(function(e){return new s.T2(((n,r)=>{const s=new XMLHttpRequest;s.onerror=r,s.onreadystatechange=()=>{s.readyState===i&&n({statusCode:s.status,headers:{"x-sentry-rate-limits":s.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":s.getResponseHeader("Retry-After")}})},s.open("POST",t.url);for(const e in t.headers)Object.prototype.hasOwnProperty.call(t.headers,e)&&s.setRequestHeader(e,t.headers[e]);s.send(e.body)}))}))}},41053:(t,e,n)=>{n.d(e,{BD:()=>a,Kg:()=>u,Qd:()=>f,Qg:()=>_,T2:()=>o,W6:()=>c,bJ:()=>s,gd:()=>h,mE:()=>g,sO:()=>l,tH:()=>y,vq:()=>d,xH:()=>p,yr:()=>m});const r=Object.prototype.toString;function s(t){switch(r.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return y(t,Error)}}function i(t,e){return r.call(t)===`[object ${e}]`}function o(t){return i(t,"ErrorEvent")}function a(t){return i(t,"DOMError")}function c(t){return i(t,"DOMException")}function u(t){return i(t,"String")}function l(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function f(t){return i(t,"Object")}function p(t){return"undefined"!=typeof Event&&y(t,Event)}function d(t){return"undefined"!=typeof Element&&y(t,Element)}function h(t){return i(t,"RegExp")}function _(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function g(t){return f(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function m(t){return"number"==typeof t&&t!=t}function y(t,e){try{return t instanceof e}catch(n){return!1}}},42202:(t,e,n)=>{n.d(e,{te:()=>s});const r=["fatal","error","warning","log","info","debug"];function s(t){return"warn"===t?"warning":r.includes(t)?t:"log"}},77451:(t,e,n)=>{n.d(e,{Z:()=>a,k:()=>c});var r=n(153822),s=n(498320);const i="7";function o(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function a(t,e={}){const n="string"==typeof e?e:e.tunnel,s="string"!=typeof e&&e._metadata?e._metadata.sdk:void 0;return n||`${function(t){return`${o(t)}${t.projectId}/envelope/`}(t)}?${function(t,e){return(0,r.u4)({sentry_key:t.publicKey,sentry_version:i,...e&&{sentry_client:`${e.name}/${e.version}`}})}(t,s)}`}function c(t,e){const n=(0,s.AD)(t),r=`${o(n)}embed/error-page/`;let i=`dsn=${(0,s.SB)(n)}`;for(const s in e)if("dsn"!==s)if("user"===s){const t=e.user;if(!t)continue;t.name&&(i+=`&name=${encodeURIComponent(t.name)}`),t.email&&(i+=`&email=${encodeURIComponent(t.email)}`)}else i+=`&${encodeURIComponent(s)}=${encodeURIComponent(e[s])}`;return`${r}?${i}`}},83253:(t,e,n)=>{n.d(e,{V:()=>m});var r=n(498320),s=n(634121),i=n(41053),o=n(978797),a=n(388945),c=n(700996),u=n(223438),l=n(179420),f=n(283671),p=n(77451),d=n(886319),h=n(119169),_=n(686903),g=n(634495);class m{__init(){this._integrations={}}__init2(){this._integrationsInitialized=!1}__init3(){this._numProcessing=0}__init4(){this._outcomes={}}constructor(t){if(m.prototype.__init.call(this),m.prototype.__init2.call(this),m.prototype.__init3.call(this),m.prototype.__init4.call(this),this._options=t,t.dsn){this._dsn=(0,r.AD)(t.dsn);const e=(0,p.Z)(this._dsn,t);this._transport=t.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}}captureException(t,e,n){if((0,s.GR)(t))return;let r=e&&e.event_id;return this._process(this.eventFromException(t,e).then((t=>this._captureEvent(t,e,n))).then((t=>{r=t}))),r}captureMessage(t,e,n,r){let s=n&&n.event_id;const o=(0,i.sO)(t)?this.eventFromMessage(String(t),e,n):this.eventFromException(t,n);return this._process(o.then((t=>this._captureEvent(t,n,r))).then((t=>{s=t}))),s}captureEvent(t,e,n){if(e&&e.originalException&&(0,s.GR)(e.originalException))return;let r=e&&e.event_id;return this._process(this._captureEvent(t,e,n).then((t=>{r=t}))),r}captureSession(t){this._isEnabled()&&("string"!=typeof t.release||(this.sendSession(t),(0,g.qO)(t,{init:!1})))}getDsn(){return this._dsn}getOptions(){return this._options}getTransport(){return this._transport}flush(t){const e=this._transport;return e?this._isClientDoneProcessing(t).then((n=>e.flush(t).then((t=>n&&t)))):(0,o.XW)(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,t)))}setupIntegrations(){this._isEnabled()&&!this._integrationsInitialized&&(this._integrations=(0,h.P$)(this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch(e){return null}}sendEvent(t,e={}){if(this._dsn){let n=(0,d.V)(t,this._dsn,this._options._metadata,this._options.tunnel);for(const t of e.attachments||[])n=(0,a.W3)(n,(0,a.bm)(t,this._options.transportOptions&&this._options.transportOptions.textEncoder));this._sendEnvelope(n)}}sendSession(t){if(this._dsn){const e=(0,d.L)(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(e)}}recordDroppedEvent(t,e){if(this._options.sendClientReports){const n=`${t}:${e}`;this._outcomes[n]=this._outcomes[n]+1||1}}_updateSessionFromEvent(t,e){let n=!1,r=!1;const s=e.exception&&e.exception.values;if(s){r=!0;for(const t of s){const e=t.mechanism;if(e&&!1===e.handled){n=!0;break}}}const i="ok"===t.status;(i&&0===t.errors||i&&n)&&((0,g.qO)(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new o.T2((e=>{let n=0;const r=setInterval((()=>{0==this._numProcessing?(clearInterval(r),e(!0)):(n+=1,t&&n>=t&&(clearInterval(r),e(!1)))}),1)}))}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._dsn}_prepareEvent(t,e,n){const{normalizeDepth:r=3,normalizeMaxBreadth:i=1e3}=this.getOptions(),a={...t,event_id:t.event_id||e.event_id||(0,s.eJ)(),timestamp:t.timestamp||(0,c.lu)()};this._applyClientOptions(a),this._applyIntegrationsMetadata(a);let u=n;e.captureContext&&(u=_.H.clone(u).update(e.captureContext));let l=(0,o.XW)(a);if(u){const t=[...e.attachments||[],...u.getAttachments()];t.length&&(e.attachments=t),l=u.applyToEvent(a,e)}return l.then((t=>"number"==typeof r&&r>0?this._normalizeEvent(t,r,i):t))}_normalizeEvent(t,e,n){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:(0,u.S8)(t.data,e,n)}})))},...t.user&&{user:(0,u.S8)(t.user,e,n)},...t.contexts&&{contexts:(0,u.S8)(t.contexts,e,n)},...t.extra&&{extra:(0,u.S8)(t.extra,e,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=(0,u.S8)(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map((t=>(t.data&&(t.data=(0,u.S8)(t.data,e,n)),t)))),r}_applyClientOptions(t){const e=this.getOptions(),{environment:n,release:r,dist:s,maxValueLength:i=250}=e;"environment"in t||(t.environment="environment"in e?n:"production"),void 0===t.release&&void 0!==r&&(t.release=r),void 0===t.dist&&void 0!==s&&(t.dist=s),t.message&&(t.message=(0,l.xv)(t.message,i));const o=t.exception&&t.exception.values&&t.exception.values[0];o&&o.value&&(o.value=(0,l.xv)(o.value,i));const a=t.request;a&&a.url&&(a.url=(0,l.xv)(a.url,i))}_applyIntegrationsMetadata(t){const e=Object.keys(this._integrations);e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}_captureEvent(t,e={},n){return this._processEvent(t,e,n).then((t=>t.event_id),(t=>{0}))}_processEvent(t,e,n){const{beforeSend:r,sampleRate:s}=this.getOptions();if(!this._isEnabled())return(0,o.xg)(new f.U("SDK not enabled, will not capture event.","log"));const a="transaction"===t.type;return!a&&"number"==typeof s&&Math.random()>s?(this.recordDroppedEvent("sample_rate","error"),(0,o.xg)(new f.U(`Discarding event because it's not included in the random sample (sampling rate = ${s})`,"log"))):this._prepareEvent(t,e,n).then((n=>{if(null===n)throw this.recordDroppedEvent("event_processor",t.type||"error"),new f.U("An event processor returned null, will not send event.","log");if(e.data&&!0===e.data.__sentry__||a||!r)return n;return function(t){const e="`beforeSend` method has to return `null` or a valid event.";if((0,i.Qg)(t))return t.then((t=>{if(!(0,i.Qd)(t)&&null!==t)throw new f.U(e);return t}),(t=>{throw new f.U(`beforeSend rejected with ${t}`)}));if(!(0,i.Qd)(t)&&null!==t)throw new f.U(e);return t}(r(n,e))})).then((r=>{if(null===r)throw this.recordDroppedEvent("before_send",t.type||"error"),new f.U("`beforeSend` returned `null`, will not send event.","log");const s=n&&n.getSession();!a&&s&&this._updateSessionFromEvent(s,r);const i=r.transaction_info;if(a&&i&&r.transaction!==t.transaction){const t="custom";r.transaction_info={...i,source:t,changes:[...i.changes,{source:t,timestamp:r.timestamp,propagations:i.propagations}]}}return this.sendEvent(r,e),r})).then(null,(t=>{if(t instanceof f.U)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new f.U(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}_process(t){this._numProcessing+=1,t.then((t=>(this._numProcessing-=1,t)),(t=>(this._numProcessing-=1,t)))}_sendEnvelope(t){this._transport&&this._dsn&&this._transport.send(t).then(null,(t=>{}))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.keys(t).map((e=>{const[n,r]=e.split(":");return{reason:n,category:r,quantity:t[e]}}))}}},119169:(t,e,n)=>{n.d(e,{P$:()=>c,mH:()=>a});var r=n(634121),s=n(591736),i=n(686903);const o=[];function a(t){const e=t.defaultIntegrations||[],n=t.integrations;let s;e.forEach((t=>{t.isDefaultInstance=!0})),s=Array.isArray(n)?[...e,...n]:"function"==typeof n?(0,r.k9)(n(e)):e;const i=function(t){const e={};return t.forEach((t=>{const{name:n}=t,r=e[n];r&&!r.isDefaultInstance&&t.isDefaultInstance||(e[n]=t)})),Object.values(e)}(s),o=i.findIndex((t=>"Debug"===t.name));if(-1!==o){const[t]=i.splice(o,1);i.push(t)}return i}function c(t){const e={};return t.forEach((t=>{e[t.name]=t,-1===o.indexOf(t.name)&&(t.setupOnce(i.l,s.BF),o.push(t.name))})),e}},153822:(t,e,n)=>{n.d(e,{Ce:()=>_,GS:()=>o,HF:()=>h,W4:()=>f,my:()=>a,pO:()=>c,sp:()=>u,u4:()=>l});var r=n(350685),s=n(41053),i=n(179420);function o(t,e,n){if(!(e in t))return;const r=t[e],s=n(r);if("function"==typeof s)try{c(s,r)}catch(i){}t[e]=s}function a(t,e,n){Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}function c(t,e){const n=e.prototype||{};t.prototype=e.prototype=n,a(t,"__sentry_original__",e)}function u(t){return t.__sentry_original__}function l(t){return Object.keys(t).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`)).join("&")}function f(t){if((0,s.bJ)(t))return{message:t.message,name:t.name,stack:t.stack,...d(t)};if((0,s.xH)(t)){const e={type:t.type,target:p(t.target),currentTarget:p(t.currentTarget),...d(t)};return"undefined"!=typeof CustomEvent&&(0,s.tH)(t,CustomEvent)&&(e.detail=t.detail),e}return t}function p(t){try{return(0,s.vq)(t)?(0,r.Hd)(t):Object.prototype.toString.call(t)}catch(e){return"<unknown>"}}function d(t){if("object"==typeof t&&null!==t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function h(t,e=40){const n=Object.keys(f(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return(0,i.xv)(n[0],e);for(let r=n.length;r>0;r--){const t=n.slice(0,r).join(", ");if(!(t.length>e))return r===n.length?t:(0,i.xv)(t,e)}return""}function _(t){return g(t,new Map)}function g(t,e){if((0,s.Qd)(t)){const n=e.get(t);if(void 0!==n)return n;const r={};e.set(t,r);for(const s of Object.keys(t))void 0!==t[s]&&(r[s]=g(t[s],e));return r}if(Array.isArray(t)){const n=e.get(t);if(void 0!==n)return n;const r=[];return e.set(t,r),t.forEach((t=>{r.push(g(t,e))})),r}return t}},174529:(t,e,n)=>{n.d(e,{i:()=>o});var r=n(716973),s=n(350685);let i;function o(){if(i)return i;if((0,r.ap)(s.jf.fetch))return i=s.jf.fetch.bind(s.jf);const t=s.jf.document;let e=s.jf.fetch;if(t&&"function"==typeof t.createElement)try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n);const r=n.contentWindow;r&&r.fetch&&(e=r.fetch),t.head.removeChild(n)}catch(n){}return i=e.bind(s.jf)}},179420:(t,e,n)=>{n.d(e,{_c:()=>a,gt:()=>o,nC:()=>i,xv:()=>s});var r=n(41053);function s(t,e=0){return"string"!=typeof t||0===e||t.length<=e?t:`${t.substr(0,e)}...`}function i(t,e){let n=t;const r=n.length;if(r<=150)return n;e>r&&(e=r);let s=Math.max(e-60,0);s<5&&(s=0);let i=Math.min(s+140,r);return i>r-5&&(i=r),i===r&&(s=Math.max(i-140,0)),n=n.slice(s,i),s>0&&(n=`'{snip} ${n}`),i<r&&(n+=" {snip}"),n}function o(t,e){if(!Array.isArray(t))return"";const n=[];for(let s=0;s<t.length;s++){const e=t[s];try{n.push(String(e))}catch(r){n.push("[value cannot be serialized]")}}return n.join(e)}function a(t,e){return!!(0,r.Kg)(t)&&((0,r.gd)(e)?e.test(t):"string"==typeof e&&-1!==t.indexOf(e))}},211390:(t,e,n)=>{function r(t){return t&&t.Math==Math?t:void 0}n.d(e,{BY:()=>i,OW:()=>s});const s="object"==typeof globalThis&&r(globalThis)||"object"==typeof window&&r(window)||"object"==typeof self&&r(self)||"object"==typeof n.g&&r(n.g)||function(){return this}()||{};function i(t,e,n){const r=n||s,i=r.__SENTRY__=r.__SENTRY__||{};return i[t]||(i[t]=e())}},223438:(t,e,n)=>{n.d(e,{S8:()=>a,cd:()=>c});var r=n(41053),s=n(623179),i=n(153822),o=n(260184);function a(t,e=1/0,n=1/0){try{return u("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function c(t,e=3,n=102400){const r=a(t,e);return s=r,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(s))>n?c(t,e-1,n):r;var s}function u(t,e,a=1/0,c=1/0,l=(0,s.s)()){const[f,p]=l;if(null===e||["number","boolean","string"].includes(typeof e)&&!(0,r.yr)(e))return e;const d=function(t,e){try{return"domain"===t&&e&&"object"==typeof e&&e._events?"[Domain]":"domainEmitter"===t?"[DomainEmitter]":void 0!==n.g&&e===n.g?"[Global]":"undefined"!=typeof window&&e===window?"[Window]":"undefined"!=typeof document&&e===document?"[Document]":(0,r.mE)(e)?"[SyntheticEvent]":"number"==typeof e&&e!=e?"[NaN]":void 0===e?"[undefined]":"function"==typeof e?`[Function: ${(0,o.qQ)(e)}]`:"symbol"==typeof e?`[${String(e)}]`:"bigint"==typeof e?`[BigInt: ${String(e)}]`:`[object ${Object.getPrototypeOf(e).constructor.name}]`}catch(s){return`**non-serializable** (${s})`}}(t,e);if(!d.startsWith("[object "))return d;if(e.__sentry_skip_normalization__)return e;if(0===a)return d.replace("object ","");if(f(e))return"[Circular ~]";const h=e;if(h&&"function"==typeof h.toJSON)try{return u("",h.toJSON(),a-1,c,l)}catch(y){}const _=Array.isArray(e)?[]:{};let g=0;const m=(0,i.W4)(e);for(const n in m){if(!Object.prototype.hasOwnProperty.call(m,n))continue;if(g>=c){_[n]="[MaxProperties ~]";break}const t=m[n];_[n]=u(n,t,a-1,c,l),g+=1}return p(e),_}},244707:(t,e,n)=>{n.d(e,{$:()=>i});var r=n(153822);let s;class i{constructor(){i.prototype.__init.call(this)}static __initStatic(){this.id="FunctionToString"}__init(){this.name=i.id}setupOnce(){s=Function.prototype.toString,Function.prototype.toString=function(...t){const e=(0,r.sp)(this)||this;return s.apply(e,t)}}}i.__initStatic()},260184:(t,e,n)=>{n.d(e,{gd:()=>s,qQ:()=>a,vk:()=>i});n(377178);const r=50;function s(...t){const e=t.sort(((t,e)=>t[0]-e[0])).map((t=>t[1]));return(t,n=0)=>{const s=[];for(const r of t.split("\n").slice(n)){const t=r.replace(/\(error: (.*)\)/,"$1");for(const n of e){const e=n(t);if(e){s.push(e);break}}}return function(t){if(!t.length)return[];let e=t;const n=e[0].function||"",s=e[e.length-1].function||"";-1===n.indexOf("captureMessage")&&-1===n.indexOf("captureException")||(e=e.slice(1));-1!==s.indexOf("sentryWrapped")&&(e=e.slice(0,-1));return e.slice(0,r).map((t=>({...t,filename:t.filename||e[0].filename,function:t.function||"?"}))).reverse()}(s)}}function i(t){return Array.isArray(t)?s(...t):t}const o="<anonymous>";function a(t){try{return t&&"function"==typeof t&&t.name||o}catch(e){return o}}},283671:(t,e,n)=>{n.d(e,{U:()=>r});class r extends Error{constructor(t,e="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=e}}},298143:(t,e,n)=>{n.d(e,{M:()=>r});const r="7.16.0"},313708:(t,e,n)=>{n.d(e,{O:()=>f});var r=n(350685),s=n(41053),i=n(784773),o=n(153822),a=n(716973);const c={},u={};function l(t){if(!u[t])switch(u[t]=!0,t){case"console":!function(){if(!("console"in r.jf))return;i.Ow.forEach((function(t){t in r.jf.console&&(0,o.GS)(r.jf.console,t,(function(e){return function(...n){p("console",{args:n,level:t}),e&&e.apply(r.jf.console,n)}}))}))}();break;case"dom":!function(){if(!("document"in r.jf))return;const t=p.bind(null,"dom"),e=v(t,!0);r.jf.document.addEventListener("click",e,!1),r.jf.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach((e=>{const n=r.jf[e]&&r.jf[e].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,o.GS)(n,"addEventListener",(function(e){return function(n,r,s){if("click"===n||"keypress"==n)try{const r=this,i=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},o=i[n]=i[n]||{refCount:0};if(!o.handler){const r=v(t);o.handler=r,e.call(this,n,r,s)}o.refCount+=1}catch(i){}return e.call(this,n,r,s)}})),(0,o.GS)(n,"removeEventListener",(function(t){return function(e,n,r){if("click"===e||"keypress"==e)try{const n=this,s=n.__sentry_instrumentation_handlers__||{},i=s[e];i&&(i.refCount-=1,i.refCount<=0&&(t.call(this,e,i.handler,r),i.handler=void 0,delete s[e]),0===Object.keys(s).length&&delete n.__sentry_instrumentation_handlers__)}catch(s){}return t.call(this,e,n,r)}})))}))}();break;case"xhr":!function(){if(!("XMLHttpRequest"in r.jf))return;const t=XMLHttpRequest.prototype;(0,o.GS)(t,"open",(function(t){return function(...e){const n=this,r=e[1],i=n.__sentry_xhr__={method:(0,s.Kg)(e[0])?e[0].toUpperCase():e[0],url:e[1]};(0,s.Kg)(r)&&"POST"===i.method&&r.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const a=function(){if(4===n.readyState){try{i.status_code=n.status}catch(t){}p("xhr",{args:e,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:n})}};return"onreadystatechange"in n&&"function"==typeof n.onreadystatechange?(0,o.GS)(n,"onreadystatechange",(function(t){return function(...e){return a(),t.apply(n,e)}})):n.addEventListener("readystatechange",a),t.apply(n,e)}})),(0,o.GS)(t,"send",(function(t){return function(...e){return this.__sentry_xhr__&&void 0!==e[0]&&(this.__sentry_xhr__.body=e[0]),p("xhr",{args:e,startTimestamp:Date.now(),xhr:this}),t.apply(this,e)}}))}();break;case"fetch":!function(){if(!(0,a.m7)())return;(0,o.GS)(r.jf,"fetch",(function(t){return function(...e){const n={args:e,fetchData:{method:d(e),url:h(e)},startTimestamp:Date.now()};return p("fetch",{...n}),t.apply(r.jf,e).then((t=>(p("fetch",{...n,endTimestamp:Date.now(),response:t}),t)),(t=>{throw p("fetch",{...n,endTimestamp:Date.now(),error:t}),t}))}}))}();break;case"history":!function(){if(!(0,a.NJ)())return;const t=r.jf.onpopstate;function e(t){return function(...e){const n=e.length>2?e[2]:void 0;if(n){const t=_,e=String(n);_=e,p("history",{from:t,to:e})}return t.apply(this,e)}}r.jf.onpopstate=function(...e){const n=r.jf.location.href,s=_;if(_=n,p("history",{from:s,to:n}),t)try{return t.apply(this,e)}catch(i){}},(0,o.GS)(r.jf.history,"pushState",e),(0,o.GS)(r.jf.history,"replaceState",e)}();break;case"error":S=r.jf.onerror,r.jf.onerror=function(t,e,n,r,s){return p("error",{column:r,error:s,line:n,msg:t,url:e}),!!S&&S.apply(this,arguments)};break;case"unhandledrejection":b=r.jf.onunhandledrejection,r.jf.onunhandledrejection=function(t){return p("unhandledrejection",t),!b||b.apply(this,arguments)};break;default:return}}function f(t,e){c[t]=c[t]||[],c[t].push(e),l(t)}function p(t,e){if(t&&c[t])for(const r of c[t]||[])try{r(e)}catch(n){}}function d(t=[]){return"Request"in r.jf&&(0,s.tH)(t[0],Request)&&t[0].method?String(t[0].method).toUpperCase():t[1]&&t[1].method?String(t[1].method).toUpperCase():"GET"}function h(t=[]){return"string"==typeof t[0]?t[0]:"Request"in r.jf&&(0,s.tH)(t[0],Request)?t[0].url:String(t[0])}let _;const g=1e3;let m,y;function v(t,e=!1){return n=>{if(!n||y===n)return;if(function(t){if("keypress"!==t.type)return!1;try{const e=t.target;if(!e||!e.tagName)return!0;if("INPUT"===e.tagName||"TEXTAREA"===e.tagName||e.isContentEditable)return!1}catch(e){}return!0}(n))return;const s="keypress"===n.type?"input":n.type;(void 0===m||function(t,e){if(!t)return!0;if(t.type!==e.type)return!0;try{if(t.target!==e.target)return!0}catch(n){}return!1}(y,n))&&(t({event:n,name:s,global:e}),y=n),clearTimeout(m),m=r.jf.setTimeout((()=>{m=void 0}),g)}}let S=null;let b=null},338869:(t,e,n)=>{n.d(e,{fj:()=>i,wD:()=>s});var r=n(657010);function s(){return!(0,r.Z)()&&"[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)}function i(t,e){return t.require(e)}t=n.hmd(t)},350685:(t,e,n)=>{n.d(e,{$N:()=>a,Hd:()=>i,jf:()=>s});var r=n(41053);const s=n(211390).OW;function i(t,e){try{let n=t;const r=5,s=80,i=[];let a=0,c=0;const u=" > ",l=u.length;let f;for(;n&&a++<r&&(f=o(n,e),!("html"===f||a>1&&c+i.length*l+f.length>=s));)i.push(f),c+=f.length,n=n.parentNode;return i.reverse().join(u)}catch(n){return"<unknown>"}}function o(t,e){const n=t,s=[];let i,o,a,c,u;if(!n||!n.tagName)return"";s.push(n.tagName.toLowerCase());const l=e&&e.length?e.filter((t=>n.getAttribute(t))).map((t=>[t,n.getAttribute(t)])):null;if(l&&l.length)l.forEach((t=>{s.push(`[${t[0]}="${t[1]}"]`)}));else if(n.id&&s.push(`#${n.id}`),i=n.className,i&&(0,r.Kg)(i))for(o=i.split(/\s+/),u=0;u<o.length;u++)s.push(`.${o[u]}`);const f=["type","name","title","alt"];for(u=0;u<f.length;u++)a=f[u],c=n.getAttribute(a),c&&s.push(`[${a}="${c}"]`);return s.join("")}function a(){try{return s.document.location.href}catch(t){return""}}},366953:(t,e,n)=>{n.d(e,{LV:()=>R,Q:()=>T,Ts:()=>j,VN:()=>N,bX:()=>C,kF:()=>$,mn:()=>O,w7:()=>D,xg:()=>k});var r=n(443755),s=n(244707),i=n(119169),o=n(818759),a=n(591736),c=n(77451),u=n(350685),l=n(260184),f=n(716973),p=n(978797),d=n(313708),h=n(846502),_=n(884088),g=n(606676),m=n(537105),y=n(715123),v=n(829687),S=n(856447),b=n(948586),E=n(785750),x=n(910944),w=n(18830);const k=[new r.Hp,new s.$,new m.h,new y.B,new v.R,new S.lt,new b.U,new E._];function j(t={}){void 0===t.defaultIntegrations&&(t.defaultIntegrations=k),void 0===t.release&&u.jf.SENTRY_RELEASE&&u.jf.SENTRY_RELEASE.id&&(t.release=u.jf.SENTRY_RELEASE.id),void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),void 0===t.sendClientReports&&(t.sendClientReports=!0);const e={...t,stackParser:(0,l.vk)(t.stackParser||g.lG),integrations:(0,i.mH)(t),transport:t.transport||((0,f.vm)()?x._:w.u)};(0,o.J)(h.y,e),t.autoSessionTracking&&function(){if(void 0===u.jf.document)return;const t=(0,a.BF)();if(!t.captureSession)return;I(t),(0,d.O)("history",(({from:t,to:e})=>{void 0!==t&&t!==e&&I((0,a.BF)())}))}()}function O(t={},e=(0,a.BF)()){if(!u.jf.document)return;const{client:n,scope:r}=e.getStackTop(),s=t.dsn||n&&n.getDsn();if(!s)return;r&&(t.user={...r.getUser(),...t.user}),t.eventId||(t.eventId=e.lastEventId());const i=u.jf.document.createElement("script");i.async=!0,i.src=(0,c.k)(s,t),t.onLoad&&(i.onload=t.onLoad);const o=u.jf.document.head||u.jf.document.body;o&&o.appendChild(i)}function T(){return(0,a.BF)().lastEventId()}function D(){}function $(t){t()}function C(t){const e=(0,a.BF)().getClient();return e?e.flush(t):(0,p.XW)(!1)}function N(t){const e=(0,a.BF)().getClient();return e?e.close(t):(0,p.XW)(!1)}function R(t){return(0,_.LV)(t)()}function I(t){t.startSession({ignoreDuration:!0}),t.captureSession()}},377178:(t,e,n)=>{function r(t){let e,n=t[0],r=1;for(;r<t.length;){const s=t[r],i=t[r+1];if(r+=2,("optionalAccess"===s||"optionalCall"===s)&&null==n)return;"access"===s||"optionalAccess"===s?(e=n,n=i(n)):"call"!==s&&"optionalCall"!==s||(n=i(((...t)=>n.call(e,...t))),e=void 0)}return n}n.d(e,{z:()=>r})},380511:(t,e,n)=>{n.d(e,{p:()=>o});var r=n(41053),s=n(223438),i=n(153822);class o{static __initStatic(){this.id="ExtraErrorData"}__init(){this.name=o.id}constructor(t){o.prototype.__init.call(this),this._options={depth:3,...t}}setupOnce(t,e){t(((t,n)=>{const r=e().getIntegration(o);return r?r.enhanceEventWithErrorData(t,n):t}))}enhanceEventWithErrorData(t,e={}){if(!e.originalException||!(0,r.bJ)(e.originalException))return t;const n=e.originalException.name||e.originalException.constructor.name,o=this._extractErrorData(e.originalException);if(o){const e={...t.contexts},a=(0,s.S8)(o,this._options.depth);return(0,r.Qd)(a)&&((0,i.my)(a,"__sentry_skip_normalization__",!0),e[n]=a),{...t,contexts:e}}return t}_extractErrorData(t){try{const e=["name","message","stack","line","column","fileName","lineNumber","columnNumber","toJSON"],n={};for(const s of Object.keys(t)){if(-1!==e.indexOf(s))continue;const i=t[s];n[s]=(0,r.bJ)(i)?i.toString():i}if("function"==typeof t.toJSON){const e=t.toJSON();for(const t of Object.keys(e)){const s=e[t];n[t]=(0,r.bJ)(s)?s.toString():s}}return n}catch(e){}return null}}o.__initStatic()},388945:(t,e,n)=>{n.d(e,{W3:()=>o,bN:()=>u,bm:()=>l,h4:()=>i,yH:()=>a,zk:()=>p});var r=n(223438),s=n(153822);function i(t,e=[]){return[t,e]}function o(t,e){const[n,r]=t;return[n,[...r,e]]}function a(t,e){t[1].forEach((t=>{const n=t[0].type;e(t,n)}))}function c(t,e){return(e||new TextEncoder).encode(t)}function u(t,e){const[n,s]=t;let i=JSON.stringify(n);function o(t){"string"==typeof i?i="string"==typeof t?i+t:[c(i,e),t]:i.push("string"==typeof t?c(t,e):t)}for(const c of s){const[t,e]=c;if(o(`\n${JSON.stringify(t)}\n`),"string"==typeof e||e instanceof Uint8Array)o(e);else{let t;try{t=JSON.stringify(e)}catch(a){t=JSON.stringify((0,r.S8)(e))}o(t)}}return"string"==typeof i?i:function(t){const e=t.reduce(((t,e)=>t+e.length),0),n=new Uint8Array(e);let r=0;for(const s of t)n.set(s,r),r+=s.length;return n}(i)}function l(t,e){const n="string"==typeof t.data?c(t.data,e):t.data;return[(0,s.Ce)({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}const f={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default"};function p(t){return f[t]}},443755:(t,e,n)=>{n.d(e,{Hp:()=>i});var r=n(179420);const s=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/];class i{static __initStatic(){this.id="InboundFilters"}__init(){this.name=i.id}constructor(t={}){this._options=t,i.prototype.__init.call(this)}setupOnce(t,e){const n=t=>{const n=e();if(n){const e=n.getIntegration(i);if(e){const i=n.getClient(),a=i?i.getOptions():{},c=function(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...s],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(e._options,a);return function(t,e){if(e.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(e){}return!1}(t))return!0;if(function(t,e){if(!e||!e.length)return!1;return function(t){if(t.message)return[t.message];if(t.exception)try{const{type:e="",value:n=""}=t.exception.values&&t.exception.values[0]||{};return[`${n}`,`${e}: ${n}`]}catch(e){return[]}return[]}(t).some((t=>e.some((e=>(0,r._c)(t,e)))))}(t,e.ignoreErrors))return!0;if(function(t,e){if(!e||!e.length)return!1;const n=o(t);return!!n&&e.some((t=>(0,r._c)(n,t)))}(t,e.denyUrls))return!0;if(!function(t,e){if(!e||!e.length)return!0;const n=o(t);return!n||e.some((t=>(0,r._c)(n,t)))}(t,e.allowUrls))return!0;return!1}(t,c)?null:t}}return t};n.id=this.name,t(n)}}function o(t){try{let n;try{n=t.exception.values[0].stacktrace.frames}catch(e){}return n?function(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch(n){return null}}i.__initStatic()},497437:(t,e,n)=>{n.r(e),n.d(e,{Breadcrumbs:()=>S.B,BrowserClient:()=>d.y,Dedupe:()=>x.U,FunctionToString:()=>s.$,GlobalHandlers:()=>y.R,HttpContext:()=>E._,Hub:()=>i.YZ,InboundFilters:()=>o.Hp,Integrations:()=>k,LinkedErrors:()=>b.lt,SDK_VERSION:()=>a.M,Scope:()=>c.H,TryCatch:()=>v.h,addBreadcrumb:()=>u.ZQ,addGlobalEventProcessor:()=>c.l,captureEvent:()=>u.r,captureException:()=>u.Cp,captureMessage:()=>u.wd,chromeStackLineParser:()=>g.Yj,close:()=>m.VN,configureScope:()=>u.PN,createTransport:()=>l.o,defaultIntegrations:()=>m.xg,defaultStackLineParsers:()=>g.c9,defaultStackParser:()=>g.lG,flush:()=>m.bX,forceLoad:()=>m.w7,geckoStackLineParser:()=>g.dY,getCurrentHub:()=>i.BF,getHubFromCarrier:()=>i.Me,init:()=>m.Ts,lastEventId:()=>m.Q,makeFetchTransport:()=>h._,makeMain:()=>i.gM,makeXHRTransport:()=>_.u,onLoad:()=>m.kF,opera10StackLineParser:()=>g.Q_,opera11StackLineParser:()=>g.Vv,setContext:()=>u.o,setExtra:()=>u.l7,setExtras:()=>u.cx,setTag:()=>u.NA,setTags:()=>u.Wt,setUser:()=>u.gV,showReportDialog:()=>m.mn,startTransaction:()=>u.nb,winjsStackLineParser:()=>g.$2,withScope:()=>u.v4,wrap:()=>m.LV});var r=n(752139),s=n(244707),i=n(591736),o=n(443755),a=n(298143),c=n(686903),u=n(762936),l=n(982745),f=n(350685),p=n(936609),d=n(846502),h=n(910944),_=n(18830),g=n(606676),m=n(366953),y=n(829687),v=n(537105),S=n(715123),b=n(856447),E=n(785750),x=n(948586);let w={};f.jf.Sentry&&f.jf.Sentry.Integrations&&(w=f.jf.Sentry.Integrations);const k={...w,...r,...p}},498320:(t,e,n)=>{n.d(e,{AD:()=>a,SB:()=>i});var r=n(283671);const s=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function i(t,e=!1){const{host:n,path:r,pass:s,port:i,projectId:o,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&s?`:${s}`:""}@${n}${i?`:${i}`:""}/${r?`${r}/`:r}${o}`}function o(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function a(t){const e="string"==typeof t?function(t){const e=s.exec(t);if(!e)throw new r.U(`Invalid Sentry Dsn: ${t}`);const[n,i,a="",c,u="",l]=e.slice(1);let f="",p=l;const d=p.split("/");if(d.length>1&&(f=d.slice(0,-1).join("/"),p=d.pop()),p){const t=p.match(/^\d+/);t&&(p=t[0])}return o({host:c,pass:a,path:f,projectId:p,port:u,protocol:n,publicKey:i})}(t):o(t);return e}},537105:(t,e,n)=>{n.d(e,{h:()=>c});var r=n(153822),s=n(350685),i=n(260184),o=n(884088);const a=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];class c{static __initStatic(){this.id="TryCatch"}__init(){this.name=c.id}constructor(t){c.prototype.__init.call(this),this._options={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t}}setupOnce(){this._options.setTimeout&&(0,r.GS)(s.jf,"setTimeout",u),this._options.setInterval&&(0,r.GS)(s.jf,"setInterval",u),this._options.requestAnimationFrame&&(0,r.GS)(s.jf,"requestAnimationFrame",l),this._options.XMLHttpRequest&&"XMLHttpRequest"in s.jf&&(0,r.GS)(XMLHttpRequest.prototype,"send",f);const t=this._options.eventTarget;if(t){(Array.isArray(t)?t:a).forEach(p)}}}function u(t){return function(...e){const n=e[0];return e[0]=(0,o.LV)(n,{mechanism:{data:{function:(0,i.qQ)(t)},handled:!0,type:"instrument"}}),t.apply(this,e)}}function l(t){return function(e){return t.apply(this,[(0,o.LV)(e,{mechanism:{data:{function:"requestAnimationFrame",handler:(0,i.qQ)(t)},handled:!0,type:"instrument"}})])}}function f(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in n&&"function"==typeof n[t]&&(0,r.GS)(n,t,(function(e){const n={mechanism:{data:{function:t,handler:(0,i.qQ)(e)},handled:!0,type:"instrument"}},s=(0,r.sp)(e);return s&&(n.mechanism.data.handler=(0,i.qQ)(s)),(0,o.LV)(e,n)}))})),t.apply(this,e)}}function p(t){const e=s.jf,n=e[t]&&e[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&((0,r.GS)(n,"addEventListener",(function(e){return function(n,r,s){try{"function"==typeof r.handleEvent&&(r.handleEvent=(0,o.LV)(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:(0,i.qQ)(r),target:t},handled:!0,type:"instrument"}}))}catch(a){}return e.apply(this,[n,(0,o.LV)(r,{mechanism:{data:{function:"addEventListener",handler:(0,i.qQ)(r),target:t},handled:!0,type:"instrument"}}),s])}})),(0,r.GS)(n,"removeEventListener",(function(t){return function(e,n,r){const s=n;try{const n=s&&s.__sentry_wrapped__;n&&t.call(this,e,n,r)}catch(i){}return t.call(this,e,s,r)}})))}c.__initStatic()},590950:(t,e,n)=>{n.d(e,{H7:()=>g,K8:()=>u,qv:()=>_,u:()=>h});var r=n(591736),s=n(41053),i=n(153822),o=n(223438),a=n(634121),c=n(978797);function u(t,e){const n=f(t,e),r={type:e&&e.name,value:d(e)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function l(t,e){return{exception:{values:[u(t,e)]}}}function f(t,e){const n=e.stacktrace||e.stack||"",r=function(t){if(t){if("number"==typeof t.framesToPop)return t.framesToPop;if(p.test(t.message))return 1}return 0}(e);try{return t(n,r)}catch(s){}return[]}const p=/Minified React error #\d+;/i;function d(t){const e=t&&t.message;return e?e.error&&"string"==typeof e.error.message?e.error.message:e:"No error message"}function h(t,e,n,r){const s=g(t,e,n&&n.syntheticException||void 0,r);return(0,a.M6)(s),s.level="error",n&&n.event_id&&(s.event_id=n.event_id),(0,c.XW)(s)}function _(t,e,n="info",r,s){const i=m(t,e,r&&r.syntheticException||void 0,s);return i.level=n,r&&r.event_id&&(i.event_id=r.event_id),(0,c.XW)(i)}function g(t,e,n,c,u){let p;if((0,s.T2)(e)&&e.error){return l(t,e.error)}if((0,s.BD)(e)||(0,s.W6)(e)){const r=e;if("stack"in e)p=l(t,e);else{const e=r.name||((0,s.BD)(r)?"DOMError":"DOMException"),i=r.message?`${e}: ${r.message}`:e;p=m(t,i,n,c),(0,a.gO)(p,i)}return"code"in r&&(p.tags={...p.tags,"DOMException.code":`${r.code}`}),p}if((0,s.bJ)(e))return l(t,e);if((0,s.Qd)(e)||(0,s.xH)(e)){return p=function(t,e,n,a){const c=(0,r.BF)().getClient(),u=c&&c.getOptions().normalizeDepth,l={exception:{values:[{type:(0,s.xH)(e)?e.constructor.name:a?"UnhandledRejection":"Error",value:`Non-Error ${a?"promise rejection":"exception"} captured with keys: ${(0,i.HF)(e)}`}]},extra:{__serialized__:(0,o.cd)(e,u)}};if(n){const e=f(t,n);e.length&&(l.exception.values[0].stacktrace={frames:e})}return l}(t,e,n,u),(0,a.M6)(p,{synthetic:!0}),p}return p=m(t,e,n,c),(0,a.gO)(p,`${e}`,void 0),(0,a.M6)(p,{synthetic:!0}),p}function m(t,e,n,r){const s={message:e};if(r&&n){const r=f(t,n);r.length&&(s.exception={values:[{value:e,stacktrace:{frames:r}}]})}return s}},591736:(t,e,n)=>{n.d(e,{BF:()=>_,Me:()=>m,YZ:()=>p,gM:()=>h});var r=n(634121),s=n(700996),i=n(784773),o=n(211390),a=n(338869),c=n(686903),u=n(634495);const l=4,f=100;class p{__init(){this._stack=[{}]}constructor(t,e=new c.H,n=l){this._version=n,p.prototype.__init.call(this),this.getStackTop().scope=e,t&&this.bindClient(t)}isOlderThan(t){return this._version<t}bindClient(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){const t=c.H.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}withScope(t){const e=this.pushScope();try{t(e)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(t,e){const n=this._lastEventId=e&&e.event_id?e.event_id:(0,r.eJ)(),s=new Error("Sentry syntheticException");return this._withClient(((r,i)=>{r.captureException(t,{originalException:t,syntheticException:s,...e,event_id:n},i)})),n}captureMessage(t,e,n){const s=this._lastEventId=n&&n.event_id?n.event_id:(0,r.eJ)(),i=new Error(t);return this._withClient(((r,o)=>{r.captureMessage(t,e,{originalException:t,syntheticException:i,...n,event_id:s},o)})),s}captureEvent(t,e){const n=e&&e.event_id?e.event_id:(0,r.eJ)();return"transaction"!==t.type&&(this._lastEventId=n),this._withClient(((r,s)=>{r.captureEvent(t,{...e,event_id:n},s)})),n}lastEventId(){return this._lastEventId}addBreadcrumb(t,e){const{scope:n,client:r}=this.getStackTop();if(!n||!r)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:a=f}=r.getOptions&&r.getOptions()||{};if(a<=0)return;const c={timestamp:(0,s.lu)(),...t},u=o?(0,i.pq)((()=>o(c,e))):c;null!==u&&n.addBreadcrumb(u,a)}setUser(t){const e=this.getScope();e&&e.setUser(t)}setTags(t){const e=this.getScope();e&&e.setTags(t)}setExtras(t){const e=this.getScope();e&&e.setExtras(t)}setTag(t,e){const n=this.getScope();n&&n.setTag(t,e)}setExtra(t,e){const n=this.getScope();n&&n.setExtra(t,e)}setContext(t,e){const n=this.getScope();n&&n.setContext(t,e)}configureScope(t){const{scope:e,client:n}=this.getStackTop();e&&n&&t(e)}run(t){const e=h(this);try{t(this)}finally{h(e)}}getIntegration(t){const e=this.getClient();if(!e)return null;try{return e.getIntegration(t)}catch(n){return null}}startTransaction(t,e){return this._callExtensionMethod("startTransaction",t,e)}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this._sendSessionUpdate()}endSession(){const t=this.getStackTop(),e=t&&t.scope,n=e&&e.getSession();n&&(0,u.Vu)(n),this._sendSessionUpdate(),e&&e.setSession()}startSession(t){const{scope:e,client:n}=this.getStackTop(),{release:r,environment:s}=n&&n.getOptions()||{},{userAgent:i}=o.OW.navigator||{},a=(0,u.fj)({release:r,environment:s,...e&&{user:e.getUser()},...i&&{userAgent:i},...t});if(e){const t=e.getSession&&e.getSession();t&&"ok"===t.status&&(0,u.qO)(t,{status:"exited"}),this.endSession(),e.setSession(a)}return a}shouldSendDefaultPii(){const t=this.getClient(),e=t&&t.getOptions();return Boolean(e&&e.sendDefaultPii)}_sendSessionUpdate(){const{scope:t,client:e}=this.getStackTop();if(!t)return;const n=t.getSession();n&&e&&e.captureSession&&e.captureSession(n)}_withClient(t){const{scope:e,client:n}=this.getStackTop();n&&t(n,e)}_callExtensionMethod(t,...e){const n=d().__SENTRY__;if(n&&n.extensions&&"function"==typeof n.extensions[t])return n.extensions[t].apply(this,e)}}function d(){return o.OW.__SENTRY__=o.OW.__SENTRY__||{extensions:{},hub:void 0},o.OW}function h(t){const e=d(),n=m(e);return y(e,t),n}function _(){const t=d();return g(t)&&!m(t).isOlderThan(l)||y(t,new p),(0,a.wD)()?function(t){try{const e=d().__SENTRY__,n=e&&e.extensions&&e.extensions.domain&&e.extensions.domain.active;if(!n)return m(t);if(!g(n)||m(n).isOlderThan(l)){const e=m(t).getStackTop();y(n,new p(e.client,c.H.clone(e.scope)))}return m(n)}catch(e){return m(t)}}(t):m(t)}function g(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function m(t){return(0,o.BY)("hub",(()=>new p),t)}function y(t,e){if(!t)return!1;return(t.__SENTRY__=t.__SENTRY__||{}).hub=e,!0}},597516:(t,e,n)=>{function r(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],relative:e[5]+n+r}}n.d(e,{Dl:()=>r})},606676:(t,e,n)=>{n.d(e,{$2:()=>d,Q_:()=>_,Vv:()=>m,Yj:()=>c,c9:()=>y,dY:()=>f,lG:()=>v});var r=n(260184);const s="?";function i(t,e,n,r){const s={filename:t,function:e,in_app:!0};return void 0!==n&&(s.lineno=n),void 0!==r&&(s.colno=r),s}const o=/^\s*at (?:(.*\).*?|.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,a=/\((\S*)(?::(\d+))(?::(\d+))\)/,c=[30,t=>{const e=o.exec(t);if(e){if(e[2]&&0===e[2].indexOf("eval")){const t=a.exec(e[2]);t&&(e[2]=t[1],e[3]=t[2],e[4]=t[3])}const[t,n]=S(e[1]||s,e[2]);return i(n,t,e[3]?+e[3]:void 0,e[4]?+e[4]:void 0)}}],u=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,l=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,f=[50,t=>{const e=u.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const t=l.exec(e[3]);t&&(e[1]=e[1]||"eval",e[3]=t[1],e[4]=t[2],e[5]="")}let t=e[3],n=e[1]||s;return[n,t]=S(n,t),i(t,n,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}],p=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,d=[40,t=>{const e=p.exec(t);return e?i(e[2],e[1]||s,+e[3],e[4]?+e[4]:void 0):void 0}],h=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,_=[10,t=>{const e=h.exec(t);return e?i(e[2],e[3]||s,+e[1]):void 0}],g=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,m=[20,t=>{const e=g.exec(t);return e?i(e[5],e[3]||e[4]||s,+e[1],+e[2]):void 0}],y=[c,f,d],v=(0,r.gd)(...y),S=(t,e)=>{const n=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return n||r?[-1!==t.indexOf("@")?t.split("@")[0]:s,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]}},623179:(t,e,n)=>{function r(){const t="function"==typeof WeakSet,e=t?new WeakSet:[];return[function(n){if(t)return!!e.has(n)||(e.add(n),!1);for(let t=0;t<e.length;t++){if(e[t]===n)return!0}return e.push(n),!1},function(n){if(t)e.delete(n);else for(let t=0;t<e.length;t++)if(e[t]===n){e.splice(t,1);break}}]}n.d(e,{s:()=>r})},634121:(t,e,n)=>{n.d(e,{$X:()=>a,GR:()=>l,M6:()=>u,eJ:()=>i,gO:()=>c,k9:()=>f});var r=n(153822),s=(n(179420),n(211390));function i(){const t=s.OW,e=t.crypto||t.msCrypto;if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");const n=e&&e.getRandomValues?()=>e.getRandomValues(new Uint8Array(1))[0]:()=>16*Math.random();return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&n())>>t/4).toString(16)))}function o(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function a(t){const{message:e,event_id:n}=t;if(e)return e;const r=o(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function c(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],i=s[0]=s[0]||{};i.value||(i.value=e||""),i.type||(i.type=n||"Error")}function u(t,e){const n=o(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const t={...r&&r.data,...e.data};n.mechanism.data=t}}function l(t){if(t&&t.__sentry_captured__)return!0;try{(0,r.my)(t,"__sentry_captured__",!0)}catch(e){}return!1}function f(t){return Array.isArray(t)?t:[t]}},634495:(t,e,n)=>{n.d(e,{Vu:()=>c,fj:()=>o,qO:()=>a});var r=n(700996),s=n(634121),i=n(153822);function o(t){const e=(0,r.zf)(),n={sid:(0,s.eJ)(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return(0,i.Ce)({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(n)};return t&&a(n,t),n}function a(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||(0,r.zf)(),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:(0,s.eJ)()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"==typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof e.duration)t.duration=e.duration;else{const e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"==typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function c(t,e){let n={};e?n={status:e}:"ok"===t.status&&(n={status:"exited"}),a(t,n)}},657010:(t,e,n)=>{function r(){return"undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&!!__SENTRY_BROWSER_BUNDLE__}n.d(e,{Z:()=>r})},686903:(t,e,n)=>{n.d(e,{H:()=>u,l:()=>f});var r=n(41053),s=n(700996),i=n(978797),o=n(634121),a=n(211390),c=n(634495);class u{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={}}static clone(t){const e=new u;return t&&(e._breadcrumbs=[...t._breadcrumbs],e._tags={...t._tags},e._extra={...t._extra},e._contexts={...t._contexts},e._user=t._user,e._level=t._level,e._span=t._span,e._session=t._session,e._transactionName=t._transactionName,e._fingerprint=t._fingerprint,e._eventProcessors=[...t._eventProcessors],e._requestSession=t._requestSession,e._attachments=[...t._attachments]),e}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{},this._session&&(0,c.qO)(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts={...this._contexts,[t]:e},this._notifyScopeListeners(),this}setSpan(t){return this._span=t,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){const t=this.getSpan();return t&&t.transaction}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;if("function"==typeof t){const e=t(this);return e instanceof u?e:this}return t instanceof u?(this._tags={...this._tags,...t._tags},this._extra={...this._extra,...t._extra},this._contexts={...this._contexts,...t._contexts},t._user&&Object.keys(t._user).length&&(this._user=t._user),t._level&&(this._level=t._level),t._fingerprint&&(this._fingerprint=t._fingerprint),t._requestSession&&(this._requestSession=t._requestSession)):(0,r.Qd)(t)&&(this._tags={...this._tags,...t.tags},this._extra={...this._extra,...t.extra},this._contexts={...this._contexts,...t.contexts},t.user&&(this._user=t.user),t.level&&(this._level=t.level),t.fingerprint&&(this._fingerprint=t.fingerprint),t.requestSession&&(this._requestSession=t.requestSession)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this}addBreadcrumb(t,e){const n="number"==typeof e?e:100;if(n<=0)return this;const r={timestamp:(0,s.lu)(),...t};return this._breadcrumbs=[...this._breadcrumbs,r].slice(-n),this._notifyScopeListeners(),this}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(t,e={}){if(this._extra&&Object.keys(this._extra).length&&(t.extra={...this._extra,...t.extra}),this._tags&&Object.keys(this._tags).length&&(t.tags={...this._tags,...t.tags}),this._user&&Object.keys(this._user).length&&(t.user={...this._user,...t.user}),this._contexts&&Object.keys(this._contexts).length&&(t.contexts={...this._contexts,...t.contexts}),this._level&&(t.level=this._level),this._transactionName&&(t.transaction=this._transactionName),this._span){t.contexts={trace:this._span.getTraceContext(),...t.contexts};const e=this._span.transaction&&this._span.transaction.name;e&&(t.tags={transaction:e,...t.tags})}return this._applyFingerprint(t),t.breadcrumbs=[...t.breadcrumbs||[],...this._breadcrumbs],t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...this._sdkProcessingMetadata},this._notifyEventProcessors([...l(),...this._eventProcessors],t,e)}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}_notifyEventProcessors(t,e,n,s=0){return new i.T2(((i,o)=>{const a=t[s];if(null===e||"function"!=typeof a)i(e);else{const c=a({...e},n);(0,r.Qg)(c)?c.then((e=>this._notifyEventProcessors(t,e,n,s+1).then(i))).then(null,o):this._notifyEventProcessors(t,c,n,s+1).then(i).then(null,o)}}))}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((t=>{t(this)})),this._notifyingListeners=!1)}_applyFingerprint(t){t.fingerprint=t.fingerprint?(0,o.k9)(t.fingerprint):[],this._fingerprint&&(t.fingerprint=t.fingerprint.concat(this._fingerprint)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}}function l(){return(0,a.BY)("globalEventProcessors",(()=>[]))}function f(t){l().push(t)}},700996:(t,e,n)=>{n.d(e,{lu:()=>c,zf:()=>u});var r=n(350685),s=n(338869);t=n.hmd(t);const i={nowSeconds:()=>Date.now()/1e3};const o=(0,s.wD)()?function(){try{return(0,s.fj)(t,"perf_hooks").performance}catch(e){return}}():function(){const{performance:t}=r.jf;if(!t||!t.now)return;return{now:()=>t.now(),timeOrigin:Date.now()-t.now()}}(),a=void 0===o?i:{nowSeconds:()=>(o.timeOrigin+o.now())/1e3},c=i.nowSeconds.bind(i),u=a.nowSeconds.bind(a);let l;(()=>{const{performance:t}=r.jf;if(!t||!t.now)return void(l="none");const e=36e5,n=t.now(),s=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+n-s):e,o=i<e,a=t.timing&&t.timing.navigationStart,c="number"==typeof a?Math.abs(a+n-s):e;o||c<e?i<=c?(l="timeOrigin",t.timeOrigin):l="navigationStart":l="dateNow"})()},715123:(t,e,n)=>{n.d(e,{B:()=>l,r:()=>u});var r=n(591736),s=n(313708),i=n(350685),o=n(42202),a=n(179420),c=n(597516);const u="Breadcrumbs";class l{static __initStatic(){this.id=u}__init(){this.name=l.id}constructor(t){l.prototype.__init.call(this),this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t}}setupOnce(){this.options.console&&(0,s.O)("console",f),this.options.dom&&(0,s.O)("dom",function(t){function e(e){let n,s="object"==typeof t?t.serializeAttribute:void 0;"string"==typeof s&&(s=[s]);try{n=e.event.target?(0,i.Hd)(e.event.target,s):(0,i.Hd)(e.event,s)}catch(o){n="<unknown>"}0!==n.length&&(0,r.BF)().addBreadcrumb({category:`ui.${e.name}`,message:n},{event:e.event,name:e.name,global:e.global})}return e}(this.options.dom)),this.options.xhr&&(0,s.O)("xhr",p),this.options.fetch&&(0,s.O)("fetch",d),this.options.history&&(0,s.O)("history",h)}}function f(t){const e={category:"console",data:{arguments:t.args,logger:"console"},level:(0,o.te)(t.level),message:(0,a.gt)(t.args," ")};if("assert"===t.level){if(!1!==t.args[0])return;e.message=`Assertion failed: ${(0,a.gt)(t.args.slice(1)," ")||"console.assert"}`,e.data.arguments=t.args.slice(1)}(0,r.BF)().addBreadcrumb(e,{input:t.args,level:t.level})}function p(t){if(t.endTimestamp){if(t.xhr.__sentry_own_request__)return;const{method:e,url:n,status_code:s,body:i}=t.xhr.__sentry_xhr__||{};(0,r.BF)().addBreadcrumb({category:"xhr",data:{method:e,url:n,status_code:s},type:"http"},{xhr:t.xhr,input:i})}else;}function d(t){t.endTimestamp&&(t.fetchData.url.match(/sentry_key/)&&"POST"===t.fetchData.method||(t.error?(0,r.BF)().addBreadcrumb({category:"fetch",data:t.fetchData,level:"error",type:"http"},{data:t.error,input:t.args}):(0,r.BF)().addBreadcrumb({category:"fetch",data:{...t.fetchData,status_code:t.response.status},type:"http"},{input:t.args,response:t.response})))}function h(t){let e=t.from,n=t.to;const s=(0,c.Dl)(i.jf.location.href);let o=(0,c.Dl)(e);const a=(0,c.Dl)(n);o.path||(o=s),s.protocol===a.protocol&&s.host===a.host&&(n=a.relative),s.protocol===o.protocol&&s.host===o.host&&(e=o.relative),(0,r.BF)().addBreadcrumb({category:"navigation",data:{from:e,to:n}})}l.__initStatic()},716973:(t,e,n)=>{n.d(e,{NJ:()=>a,ap:()=>i,m7:()=>o,vm:()=>s});var r=n(350685);function s(){if(!("fetch"in r.jf))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function i(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function o(){if(!s())return!1;if(i(r.jf.fetch))return!0;let t=!1;const e=r.jf.document;if(e&&"function"==typeof e.createElement)try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=i(n.contentWindow.fetch)),e.head.removeChild(n)}catch(n){}return t}function a(){const t=r.jf.chrome,e=t&&t.app&&t.app.runtime,n="history"in r.jf&&!!r.jf.history.pushState&&!!r.jf.history.replaceState;return!e&&n}},752139:(t,e,n)=>{n.r(e),n.d(e,{FunctionToString:()=>r.$,InboundFilters:()=>s.Hp});var r=n(244707),s=n(443755)},762936:(t,e,n)=>{n.d(e,{Cp:()=>s,NA:()=>d,PN:()=>a,Wt:()=>p,ZQ:()=>c,cx:()=>l,gV:()=>h,l7:()=>f,nb:()=>g,o:()=>u,r:()=>o,v4:()=>_,wd:()=>i});var r=n(591736);function s(t,e){return(0,r.BF)().captureException(t,{captureContext:e})}function i(t,e){const n="string"==typeof e?e:void 0,s="string"!=typeof e?{captureContext:e}:void 0;return(0,r.BF)().captureMessage(t,n,s)}function o(t,e){return(0,r.BF)().captureEvent(t,e)}function a(t){(0,r.BF)().configureScope(t)}function c(t){(0,r.BF)().addBreadcrumb(t)}function u(t,e){(0,r.BF)().setContext(t,e)}function l(t){(0,r.BF)().setExtras(t)}function f(t,e){(0,r.BF)().setExtra(t,e)}function p(t){(0,r.BF)().setTags(t)}function d(t,e){(0,r.BF)().setTag(t,e)}function h(t){(0,r.BF)().setUser(t)}function _(t){(0,r.BF)().withScope(t)}function g(t,e){return(0,r.BF)().startTransaction({...t},e)}},784190:(t,e,n)=>{n.d(e,{C:()=>i});var r=n(283671),s=n(978797);function i(t){const e=[];function n(t){return e.splice(e.indexOf(t),1)[0]}return{$:e,add:function(i){if(!(void 0===t||e.length<t))return(0,s.xg)(new r.U("Not adding Promise because buffer limit was reached."));const o=i();return-1===e.indexOf(o)&&e.push(o),o.then((()=>n(o))).then(null,(()=>n(o).then(null,(()=>{})))),o},drain:function(t){return new s.T2(((n,r)=>{let i=e.length;if(!i)return n(!0);const o=setTimeout((()=>{t&&t>0&&n(!1)}),t);e.forEach((t=>{(0,s.XW)(t).then((()=>{--i||(clearTimeout(o),n(!0))}),r)}))}))}}}},784773:(t,e,n)=>{n.d(e,{Ow:()=>s,pq:()=>i});var r=n(211390);const s=["debug","info","warn","error","log","assert","trace"];function i(t){if(!("console"in r.OW))return t();const e=r.OW.console,n={};s.forEach((t=>{const r=e[t]&&e[t].__sentry_original__;t in e&&r&&(n[t]=e[t],e[t]=r)}));try{return t()}finally{Object.keys(n).forEach((t=>{e[t]=n[t]}))}}let o;o=function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1}};return s.forEach((t=>{e[t]=()=>{}})),e}()},785750:(t,e,n)=>{n.d(e,{_:()=>o});var r=n(686903),s=n(591736),i=n(350685);class o{constructor(){o.prototype.__init.call(this)}static __initStatic(){this.id="HttpContext"}__init(){this.name=o.id}setupOnce(){(0,r.l)((t=>{if((0,s.BF)().getIntegration(o)){if(!i.jf.navigator&&!i.jf.location&&!i.jf.document)return t;const e=t.request&&t.request.url||i.jf.location&&i.jf.location.href,{referrer:n}=i.jf.document||{},{userAgent:r}=i.jf.navigator||{},s={...e&&{url:e},headers:{...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}}};return{...t,request:s}}return t}))}}o.__initStatic()},796328:(t,e,n)=>{n.d(e,{m:()=>i});var r=n(388945),s=n(700996);function i(t,e,n){const i=[{type:"client_report"},{timestamp:n||(0,s.lu)(),discarded_events:t}];return(0,r.h4)(e?{dsn:e}:{},[i])}},818759:(t,e,n)=>{n.d(e,{J:()=>s});var r=n(591736);function s(t,e){!0===e.debug&&console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.");const n=(0,r.BF)(),s=n.getScope();s&&s.update(e.initialScope);const i=new t(e);n.bindClient(i)}},829687:(t,e,n)=>{n.d(e,{R:()=>l});var r=n(591736),s=n(313708),i=n(41053),o=n(350685),a=n(634121),c=n(590950),u=n(884088);class l{static __initStatic(){this.id="GlobalHandlers"}__init(){this.name=l.id}__init2(){this._installFunc={onerror:f,onunhandledrejection:p}}constructor(t){l.prototype.__init.call(this),l.prototype.__init2.call(this),this._options={onerror:!0,onunhandledrejection:!0,...t}}setupOnce(){Error.stackTraceLimit=50;const t=this._options;for(const e in t){const n=this._installFunc[e];n&&t[e]&&(e,n(),this._installFunc[e]=void 0)}}}function f(){(0,s.O)("error",(t=>{const[e,n,r]=_();if(!e.getIntegration(l))return;const{msg:s,url:o,line:a,column:f,error:p}=t;if((0,u.jN)()||p&&p.__sentry_own_request__)return;const g=void 0===p&&(0,i.Kg)(s)?function(t,e,n,r){const s=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;let o=(0,i.T2)(t)?t.message:t,a="Error";const c=o.match(s);c&&(a=c[1],o=c[2]);const u={exception:{values:[{type:a,value:o}]}};return d(u,e,n,r)}(s,o,a,f):d((0,c.H7)(n,p||s,void 0,r,!1),o,a,f);g.level="error",h(e,p,g,"onerror")}))}function p(){(0,s.O)("unhandledrejection",(t=>{const[e,n,r]=_();if(!e.getIntegration(l))return;let s=t;try{"reason"in t?s=t.reason:"detail"in t&&"reason"in t.detail&&(s=t.detail.reason)}catch(a){}if((0,u.jN)()||s&&s.__sentry_own_request__)return!0;const o=(0,i.sO)(s)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(s)}`}]}}:(0,c.H7)(n,s,void 0,r,!0);o.level="error",h(e,s,o,"onunhandledrejection")}))}function d(t,e,n,r){const s=t.exception=t.exception||{},a=s.values=s.values||[],c=a[0]=a[0]||{},u=c.stacktrace=c.stacktrace||{},l=u.frames=u.frames||[],f=isNaN(parseInt(r,10))?void 0:r,p=isNaN(parseInt(n,10))?void 0:n,d=(0,i.Kg)(e)&&e.length>0?e:(0,o.$N)();return 0===l.length&&l.push({colno:f,filename:d,function:"?",in_app:!0,lineno:p}),t}function h(t,e,n,r){(0,a.M6)(n,{handled:!1,type:r}),t.captureEvent(n,{originalException:e})}function _(){const t=(0,r.BF)(),e=t.getClient(),n=e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[t,n.stackParser,n.attachStacktrace]}l.__initStatic()},846502:(t,e,n)=>{n.d(e,{y:()=>h});var r=n(83253),s=n(298143),i=n(591736),o=n(77451),a=n(350685),c=n(634121),u=n(796328),l=n(498320),f=n(388945),p=n(590950),d=n(715123);class h extends r.V{constructor(t){t._metadata=t._metadata||{},t._metadata.sdk=t._metadata.sdk||{name:"sentry.javascript.browser",packages:[{name:"npm:@sentry/browser",version:s.M}],version:s.M},super(t),t.sendClientReports&&a.jf.document&&a.jf.document.addEventListener("visibilitychange",(()=>{"hidden"===a.jf.document.visibilityState&&this._flushOutcomes()}))}eventFromException(t,e){return(0,p.u)(this._options.stackParser,t,e,this._options.attachStacktrace)}eventFromMessage(t,e="info",n){return(0,p.qv)(this._options.stackParser,t,e,n,this._options.attachStacktrace)}sendEvent(t,e){const n=this.getIntegrationById(d.r);n&&n.options&&n.options.sentry&&(0,i.BF)().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:(0,c.$X)(t)},{event:t}),super.sendEvent(t,e)}_prepareEvent(t,e,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,e,n)}_flushOutcomes(){const t=this._clearOutcomes();if(0===t.length)return;if(!this._dsn)return;const e=(0,o.Z)(this._dsn,this._options),n=(0,u.m)(t,this._options.tunnel&&(0,l.SB)(this._dsn));try{const t="[object Navigator]"===Object.prototype.toString.call(a.jf&&a.jf.navigator);if(t&&"function"==typeof a.jf.navigator.sendBeacon&&!this._options.transportOptions){a.jf.navigator.sendBeacon.bind(a.jf.navigator)(e,(0,f.bN)(n))}else this._sendEnvelope(n)}catch(r){}}}},856447:(t,e,n)=>{n.d(e,{lt:()=>a});var r=n(591736),s=n(686903),i=n(41053),o=n(590950);class a{static __initStatic(){this.id="LinkedErrors"}__init(){this.name=a.id}constructor(t={}){a.prototype.__init.call(this),this._key=t.key||"cause",this._limit=t.limit||5}setupOnce(){const t=(0,r.BF)().getClient();t&&(0,s.l)(((e,n)=>{const s=(0,r.BF)().getIntegration(a);return s?function(t,e,n,r,s){if(!(r.exception&&r.exception.values&&s&&(0,i.tH)(s.originalException,Error)))return r;const o=c(t,n,s.originalException,e);return r.exception.values=[...o,...r.exception.values],r}(t.getOptions().stackParser,s._key,s._limit,e,n):e}))}}function c(t,e,n,r,s=[]){if(!(0,i.tH)(n[r],Error)||s.length+1>=e)return s;const a=(0,o.K8)(t,n[r]);return c(t,e,n[r],r,[a,...s])}a.__initStatic()},884088:(t,e,n)=>{n.d(e,{LV:()=>c,jN:()=>a});var r=n(762936),s=n(153822),i=n(634121);let o=0;function a(){return o>0}function c(t,e={},n){if("function"!=typeof t)return t;try{const e=t.__sentry_wrapped__;if(e)return e;if((0,s.sp)(t))return t}catch(u){return t}const a=function(){const s=Array.prototype.slice.call(arguments);try{n&&"function"==typeof n&&n.apply(this,arguments);const r=s.map((t=>c(t,e)));return t.apply(this,r)}catch(a){throw o+=1,setTimeout((()=>{o-=1})),(0,r.v4)((t=>{t.addEventProcessor((t=>(e.mechanism&&((0,i.gO)(t,void 0,void 0),(0,i.M6)(t,e.mechanism)),t.extra={...t.extra,arguments:s},t))),(0,r.Cp)(a)})),a}};try{for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(a[e]=t[e])}catch(l){}(0,s.pO)(a,t),(0,s.my)(t,"__sentry_wrapped__",a);try{Object.getOwnPropertyDescriptor(a,"name").configurable&&Object.defineProperty(a,"name",{get:()=>t.name})}catch(l){}return a}},886319:(t,e,n)=>{n.d(e,{L:()=>a,V:()=>c});var r=n(498320),s=n(388945),i=n(153822);function o(t){if(!t||!t.sdk)return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function a(t,e,n,i){const a=o(n),c={sent_at:(new Date).toISOString(),...a&&{sdk:a},...!!i&&{dsn:(0,r.SB)(e)}},u="aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t];return(0,s.h4)(c,[u])}function c(t,e,n,a){const c=o(n),u=t.type||"event";!function(t,e){e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]])}(t,n&&n.sdk);const l=function(t,e,n,s){const o=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&{sdk:e},...!!n&&{dsn:(0,r.SB)(s)},..."transaction"===t.type&&o&&{trace:(0,i.Ce)({...o})}}}(t,c,a,e);delete t.sdkProcessingMetadata;const f=[{type:u},t];return(0,s.h4)(l,[f])}},910944:(t,e,n)=>{n.d(e,{_:()=>i});var r=n(982745),s=n(174529);function i(t,e=(0,s.i)()){return(0,r.o)(t,(function(n){const r={body:n.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n.body.length<=65536,...t.fetchOptions};return e(t.url,r).then((t=>({statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}))}},931028:(t,e,n)=>{n.d(e,{U:()=>r});class r{constructor(){r.prototype.__init.call(this)}static __initStatic(){this.id="Dedupe"}__init(){this.name=r.id}setupOnce(t,e){const n=t=>{const n=e().getIntegration(r);if(n){try{if(function(t,e){if(!e)return!1;if(function(t,e){const n=t.message,r=e.message;if(!n&&!r)return!1;if(n&&!r||!n&&r)return!1;if(n!==r)return!1;if(!i(t,e))return!1;if(!s(t,e))return!1;return!0}(t,e))return!0;if(function(t,e){const n=o(e),r=o(t);if(!n||!r)return!1;if(n.type!==r.type||n.value!==r.value)return!1;if(!i(t,e))return!1;if(!s(t,e))return!1;return!0}(t,e))return!0;return!1}(t,n._previousEvent))return null}catch(a){return n._previousEvent=t}return n._previousEvent=t}return t};n.id=this.name,t(n)}}function s(t,e){let n=a(t),r=a(e);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const t=r[s],e=n[s];if(t.filename!==e.filename||t.lineno!==e.lineno||t.colno!==e.colno||t.function!==e.function)return!1}return!0}function i(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(s){return!1}}function o(t){return t.exception&&t.exception.values&&t.exception.values[0]}function a(t){const e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch(n){return}}r.__initStatic()},936609:(t,e,n)=>{n.r(e),n.d(e,{Breadcrumbs:()=>i.B,Dedupe:()=>c.U,GlobalHandlers:()=>r.R,HttpContext:()=>a._,LinkedErrors:()=>o.lt,TryCatch:()=>s.h});var r=n(829687),s=n(537105),i=n(715123),o=n(856447),a=n(785750),c=n(948586)},947604:(t,e,n)=>{n.d(e,{Jz:()=>s,wq:()=>i});const r=6e4;function s(t,e,n=Date.now()){return function(t,e){return t[e]||t.all||0}(t,e)>n}function i(t,{statusCode:e,headers:n},s=Date.now()){const i={...t},o=n&&n["x-sentry-rate-limits"],a=n&&n["retry-after"];if(o)for(const r of o.trim().split(",")){const[t,e]=r.split(":",2),n=parseInt(t,10),o=1e3*(isNaN(n)?60:n);if(e)for(const r of e.split(";"))i[r]=s+o;else i.all=s+o}else a?i.all=s+function(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return 1e3*n;const s=Date.parse(`${t}`);return isNaN(s)?r:s-e}(a,s):429===e&&(i.all=s+6e4);return i}},948586:(t,e,n)=>{n.d(e,{U:()=>r});class r{constructor(){r.prototype.__init.call(this)}static __initStatic(){this.id="Dedupe"}__init(){this.name=r.id}setupOnce(t,e){const n=t=>{const n=e().getIntegration(r);if(n){try{if(function(t,e){if(!e)return!1;if(function(t,e){const n=t.message,r=e.message;if(!n&&!r)return!1;if(n&&!r||!n&&r)return!1;if(n!==r)return!1;if(!i(t,e))return!1;if(!s(t,e))return!1;return!0}(t,e))return!0;if(function(t,e){const n=o(e),r=o(t);if(!n||!r)return!1;if(n.type!==r.type||n.value!==r.value)return!1;if(!i(t,e))return!1;if(!s(t,e))return!1;return!0}(t,e))return!0;return!1}(t,n._previousEvent))return null}catch(a){return n._previousEvent=t}return n._previousEvent=t}return t};n.id=this.name,t(n)}}function s(t,e){let n=a(t),r=a(e);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const t=r[s],e=n[s];if(t.filename!==e.filename||t.lineno!==e.lineno||t.colno!==e.colno||t.function!==e.function)return!1}return!0}function i(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(s){return!1}}function o(t){return t.exception&&t.exception.values&&t.exception.values[0]}function a(t){const e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch(n){return}}r.__initStatic()},978797:(t,e,n)=>{n.d(e,{T2:()=>a,XW:()=>i,xg:()=>o});var r,s=n(41053);function i(t){return new a((e=>{e(t)}))}function o(t){return new a(((e,n)=>{n(t)}))}!function(t){t[t.PENDING=0]="PENDING";t[t.RESOLVED=1]="RESOLVED";t[t.REJECTED=2]="REJECTED"}(r||(r={}));class a{__init(){this._state=r.PENDING}__init2(){this._handlers=[]}constructor(t){a.prototype.__init.call(this),a.prototype.__init2.call(this),a.prototype.__init3.call(this),a.prototype.__init4.call(this),a.prototype.__init5.call(this),a.prototype.__init6.call(this);try{t(this._resolve,this._reject)}catch(e){this._reject(e)}}then(t,e){return new a(((n,r)=>{this._handlers.push([!1,e=>{if(t)try{n(t(e))}catch(s){r(s)}else n(e)},t=>{if(e)try{n(e(t))}catch(s){r(s)}else r(t)}]),this._executeHandlers()}))}catch(t){return this.then((t=>t),t)}finally(t){return new a(((e,n)=>{let r,s;return this.then((e=>{s=!1,r=e,t&&t()}),(e=>{s=!0,r=e,t&&t()})).then((()=>{s?n(r):e(r)}))}))}__init3(){this._resolve=t=>{this._setResult(r.RESOLVED,t)}}__init4(){this._reject=t=>{this._setResult(r.REJECTED,t)}}__init5(){this._setResult=(t,e)=>{this._state===r.PENDING&&((0,s.Qg)(e)?e.then(this._resolve,this._reject):(this._state=t,this._value=e,this._executeHandlers()))}}__init6(){this._executeHandlers=()=>{if(this._state===r.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach((t=>{t[0]||(this._state===r.RESOLVED&&t[1](this._value),this._state===r.REJECTED&&t[2](this._value),t[0]=!0)}))}}}},982745:(t,e,n)=>{n.d(e,{o:()=>u});var r=n(784190),s=n(388945),i=n(947604),o=n(978797),a=n(283671);const c=30;function u(t,e,n=(0,r.C)(t.bufferSize||c)){let u={};return{send:function(r){const c=[];if((0,s.yH)(r,((e,n)=>{const r=(0,s.zk)(n);(0,i.Jz)(u,r)?t.recordDroppedEvent("ratelimit_backoff",r):c.push(e)})),0===c.length)return(0,o.XW)();const l=(0,s.h4)(r[0],c),f=e=>{(0,s.yH)(l,((n,r)=>{t.recordDroppedEvent(e,(0,s.zk)(r))}))};return n.add((()=>e({body:(0,s.bN)(l,t.textEncoder)}).then((t=>{void 0!==t.statusCode&&(t.statusCode<200||t.statusCode),u=(0,i.wq)(u,t)}),(t=>{f("network_error")})))).then((t=>t),(t=>{if(t instanceof a.U)return f("queue_overflow"),(0,o.XW)();throw t}))},flush:t=>n.drain(t)}}}},e={};function n(r){var s=e[r];if(void 0!==s)return s.exports;var i=e[r]={id:r,loaded:!1,exports:{}};return t[r](i,i.exports,n),i.loaded=!0,i.exports}n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.hmd=t=>((t=Object.create(t)).children||(t.children=[]),Object.defineProperty(t,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+t.id)}}),t),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r=n(366953),s=n(762936),i=n(497437),o=n(931028),a=n(380511);!self.Sentry&&self.__sentry_config__?(r.Ts(self.__sentry_config__),s.NA("initLocation","sentry_chunk")):self.Sentry&&self.__sentry_config__?Math.random()<.001&&self.Sentry.captureMessage("Legacy sentry installed"):self.Sentry&&!self.__sentry_config__&&Math.random()<.001&&self.Sentry.captureMessage("Legacy sentry installed but new Sentry not configured"),self.Sentry||(self.Sentry=i),self.SentryIntegrations||(self.SentryIntegrations={Dedupe:o.U,ExtraErrorData:a.p})})();
//# sourceMappingURL=sourcemaps/898fc052e78d6a28.sentry_browser.js.map