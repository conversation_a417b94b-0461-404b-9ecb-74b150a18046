"use strict";var TokenEx=function(){function o(o,s){function ii(n,t){switch(n){case"load":d=t;break;case"reset":g=t;break;case"focus":nt=t;break;case"blur":tt=t;break;case"change":it=t;break;case"cvvFocus":rt=t;break;case"cvvBlur":ut=t;break;case"cvvChange":ft=t;break;case"tokenize":et=t;break;case"validate":ot=t;break;case"error":w=t;break;case"cardTypeChange":st=t;break;case"notice":p=t;break;case"forterInit":ht=t;break;case"expired":ct=t;break;case"toggleMask":lt=t;break;case"toggleCvvMask":at=t;break;case"setPAN":bt=t;break;case"kountDataCollectionBegin":vt=t;break;case"kountDataCollectionEnd":yt=t;break;case"autoCompleteValues":pt=t;break;case"binLookup":wt=t}}function ri(){var t=c.debug,n=gt();c.debug=!0;v({valid:n.valid,details:n.details});c.debug=t;return}function ui(){var f,i,u;v(c);try{c.customDataTypes!==""&&typeof c.customDataTypes=="string"&&(c.customDataTypes=r(c.customDataTypes))}catch(e){c.debug=!0;v("Error parsing customDataTypes");return}try{c.fraudServices!==""&&typeof c.fraudServices=="string"&&(c.fraudServices=r(c.fraudServices))}catch(e){c.debug=!0;v("Error parsing fraudServices");return}if(f=gt(),!f.valid){c.debug=!0;v({message:"Invalid Config Object",details:f.details});w({error:"Invalid Config Object",details:f.details});return}if(!t(a)){c.debug=!0;v({message:"Invalid Config Object",details:["Invalid container ID property"]});w({error:"Invalid Config Object",details:["Invalid container ID property"]});return}i=document.getElementById("tx_iframe_"+a.id);window.addEventListener?addEventListener("message",b,!1):attachEvent("onmessage",b);t(i)||(i=document.createElement("iframe"),i.setAttribute("src",kt(c.cvvOnly?"CVV":"Data")),i.setAttribute("frameborder","0"),i.setAttribute("width","100%"),i.setAttribute("height","100%"),i.setAttribute("id","tx_iframe_"+a.id),i.setAttribute("name","tx_iframe_"+a.id),a.appendChild(i));c.cvv&&!c.cvvOnly&&(u=document.getElementById("tx_iframe_cvv_"+y.id),t(u)||(u=document.createElement("iframe"),u.setAttribute("src",kt("CVV")),u.setAttribute("frameborder","0"),u.setAttribute("width","100%"),u.setAttribute("height","100%"),u.setAttribute("id","tx_iframe_cvv_"+y.id),u.setAttribute("name","tx_iframe_cvv_"+y.id),y.appendChild(u)));c.fraudServices!=null&&c.fraudServices.useKount!==undefined&&n(c.fraudServices.useKount)&&(c.fraudServices.kount.mode===undefined||c.fraudServices.kount.mode!==undefined&&c.fraudServices.kount.mode.toUpperCase()!=="P")&&nu();dr()}function kt(n){var t=i+"/iframe/v3?AuthenticationKey="+encodeURIComponent(c.authenticationKey)+"&Origin="+encodeURIComponent(c.origin)+"&TokenExID="+c.tokenExID+"&Timestamp="+c.timestamp+"&Container="+a.id+"&Mode="+n+"&PCI="+(c.pci?"true":"false")+"&EnforceLuhnCompliance="+c.enforceLuhnCompliance;return c.cvvOnly&&(t+="&Token="+c.token+"&CvvOnly=true"),c.cvv&&(t+="&CvvContainer="+c.cvvContainerID,t+="&CVV=true"),c.tokenScheme&&(t+="&TokenScheme="+c.tokenScheme),c.customDataLabel&&(t+="&CustomDataLabel="+c.customDataLabel),c.customCvvDataLabel&&(t+="&CustomCvvDataLabel="+c.customCvvDataLabel),c.title&&(t+="&Title="+encodeURIComponent(c.title)),c.cvvTitle&&(t+="&CvvTitle="+encodeURIComponent(c.cvvTitle)),c.inputTitle&&(t+="&InputTitle="+encodeURIComponent(c.inputTitle)),c.cvvInputTitle&&(t+="&CvvInputTitle="+encodeURIComponent(c.cvvInputTitle)),c.returnKhash&&(t+="&ReturnKhash="+c.returnKhash),c.returnWhash&&(t+="&ReturnWhash="+c.returnWhash),c.expiresInSeconds&&(t+="&ExpiresInSeconds="+c.expiresInSeconds),c.use3DS&&(t+="&Use3ds="+c.use3DS,t+="&ThreeDSMethodNotificationUrl="+c.threeDSMethodNotificationUrl),c.fraudServices&&c.fraudServices.useKount&&(t+="&UseKount="+c.fraudServices.useKount,t+="&KountMerchantId="+c.fraudServices.kount.merchantId,c.fraudServices.kount.mode&&(t+="&KountMode="+c.fraudServices.kount.mode),c.fraudServices.kount.anId&&(t+="&KountAnId="+c.fraudServices.kount.anId)),!c.pci&&c.inputMaxLength&&(t+="&InputMaxLength="+c.inputMaxLength),c.pci&&c.cardMaxLengths&&(c.cardMaxLengths.visa&&(t+="&CardMaxLengths.Visa="+c.cardMaxLengths.visa),c.cardMaxLengths.mastercard&&(t+="&CardMaxLengths.Mastercard="+c.cardMaxLengths.mastercard),c.cardMaxLengths.americanExpress&&(t+="&CardMaxLengths.AmericanExpress="+c.cardMaxLengths.americanExpress),c.cardMaxLengths.discover&&(t+="&CardMaxLengths.Discover="+c.cardMaxLengths.discover),c.cardMaxLengths.jcb&&(t+="&CardMaxLengths.Jcb="+c.cardMaxLengths.jcb),c.cardMaxLengths.diners&&(t+="&CardMaxLengths.Diners="+c.cardMaxLengths.diners)),c.useExtendedBIN&&(t+="&UseExtendedBIN="+c.useExtendedBIN),c.returnAutoCompleteValues&&c.enableAutoComplete&&(t+="&ReturnAutoCompleteValues=true"),c.inlineIframeJavaScript&&(t+="&InlineIframeJavaScript=true"),c.iframeVersion&&(t+="&IframeVersion="+c.iframeVersion),t}function fi(){c.styles&&ci(c.styles);(c.inputType||c.maskInput&&!c.inputMaxLength)&&pi(c.maskInput?"password":c.inputType);c.pci&&(typeof c.customDataTypes!="undefined"&&c.customDataTypes!==""&&pr(c.customDataTypes),c.allowUnknownCardTypes&&lr(),c.enablePrettyFormat&&!c.maskInput&&or(),c.cvv&&(bi(c.cardType),c.cvvOnly?c.cvvPlaceholder&&dt(c.cvvPlaceholder):(c.cvvPlaceholder&&hi(c.cvvPlaceholder),(c.cvvInputType||c.maskInput)&&wi(c.maskInput?"password":c.cvvInputType))),!c.cvvOnly&&c.forterSiteId&&c.forterSiteId.length>0&&c.forterUsername&&c.forterUsername.length>0&&ki(c.forterSiteId,c.forterUsername));!c.pci&&c.customRegEx&&c.customRegEx.length>0&&ai(c.customRegEx);c.placeholder&&dt(c.placeholder);c.enableAutoComplete&&si();c.enableValidateOnBlur&&vi();c.enableAriaRequired&&yi();c.debug&&yr();c.font&&vr(c.font);c.enableValidateOnKeyUp&&sr();c.enableValidateOnCvvKeyUp&&hr();c.inputMode&&di(c.inputMode);c.cardMaxLengths&&gi(c.cardMaxLengths)}function b(n){var t,u;if(n&&n.data&&n.data!=="undefined"&&n.origin===i&&(t=r(n.data),v(t),t&&t.container&&t.container===a.id))switch(t.event){case"load":fi();d();break;case"cardTypeChange":st(t.data);break;case"focus":nt();break;case"blur":tt();break;case"reset":g();break;case"change":it();break;case"cvvFocus":rt();break;case"cvvBlur":ut();break;case"cvvChange":ft();break;case"error":w(t.data);break;case"validate":ot(t.data);break;case"tokenize":if(et(t.data),c.use3DS){if(u=gr(t.data.threeDSecureResponse,t.data.referenceNumber),u.details.length){v({message:"3DS Device Fingerprinting Failed",details:u.details});p({type:"3DS Device Fingerprinting",success:!1});break}p({type:"3DS Device Fingerprinting",success:!0})}break;case"forterInit":ht(t.data);break;case"expired":ct();break;case"toggleMask":lt(t.data);break;case"toggleCvvMask":at(t.data);break;case"setPAN":bt(t.data);break;case"kountDataCollectionBegin":vt(t.data);break;case"kountDataCollectionEnd":if(t!==undefined&&t.data!==undefined&&t.data.MercSessId!==undefined&&t.data.MercSessId!==""){l({command:"kountDataCollectionEnd",data:t.data});yt(t.data);p({type:"Kount Data Collection Finished.",success:!0});break}p({type:"Kount Data Collection Failed.",success:!1});break;case"autoCompleteValues":pt(t.data);break;case"binLookup":wt(t.data)}}function ei(){l({command:"tokenize"})}function oi(){l({command:"validate"})}function dt(n){l({command:"setPlaceholder",data:n})}function si(){l({command:"enableAutoComplete"})}function hi(n){l({command:"setCvvPlaceholder",data:n})}function ci(n){l({command:"setStyle",data:n})}function li(){l({command:"reset"})}function ai(n){l({command:"setCustomRegEx",data:n})}function vi(){l({command:"enableValidateOnBlur"})}function yi(){l({command:"enableAriaRequired"})}function pi(n){l({command:"setType",data:n})}function wi(n){l({command:"setCvvType",data:n})}function bi(n){l({command:"setCardType",data:n})}function ki(n,t){l({command:"enableForterIntegration",data:{forterSiteId:n,forterUsername:t}})}function di(n){l({command:"setInputMode",data:n})}function gi(n){l({command:"setCardMaxLengths",data:n})}function nr(){l({command:"focus"})}function tr(){l({command:"cvvFocus"})}function ir(){l({command:"toggleMask"})}function rr(){l({command:"toggleCvvMask"})}function ur(n){l({command:"setPAN",data:n})}function fr(){l({command:"blur"})}function er(){l({command:"cvvBlur"})}function or(){c.pci&&l({command:"enablePrettyFormat"})}function sr(){l({command:"enableValidateOnKeyUp"})}function hr(){l({command:"enableValidateOnCvvKeyUp"})}function cr(){l({command:"binLookup"})}function lr(){c.pci&&l({command:"enableUnknownCardTypes"})}function l(n){var r=document.getElementById("tx_iframe_"+a.id);t(r)&&r.contentWindow.postMessage(JSON.stringify(n),i)}function ar(){var i=document.getElementById("tx_iframe_"+a.id),n;t(i)&&i.parentNode.removeChild(i);y&&(n=document.getElementById("tx_iframe_cvv_"+y.id),t(n)&&n.parentNode.removeChild(n));window.removeEventListener?removeEventListener("message",b,!1):detachEvent("onmessage",b);clearTimeout(u)}function vr(n){l({command:"setFont",data:n})}function yr(){l({command:"enableDebug"})}function pr(n){l({command:"setCustomDataTypes",data:n})}function v(n){window.console&&c.debug&&console.log("TokenEx Iframe Debug: "+JSON.stringify(n))}function gt(){var t=[],r,u,i;return(c.origin?(r=c.origin.split(",")[0],f!==r&&t.push("Expected origin of "+r+" but current origin is "+f)):t.push("Missing origin property"),c.tokenExID||t.push("Missing tokenExID property"),isNaN(Number(c.tokenExID))&&t.push("Invalid tokenExID property. TokenExID value must be numeric"),c.tokenScheme||t.push("Missing tokenScheme property"),c.authenticationKey||t.push("Missing authenticationKey property"),c.timestamp||t.push("Missing timestamp property"),c.use3DS)?(c.pci||t.push("Missing pci property"),c.threeDSMethodNotificationUrl.trim()||t.push("Missing threeDSMethodNotificationUrl property"),c.cvvOnly&&t.push("Invalid property cvvOnly for use3DS configuration"),i=t.length===0,{valid:i,details:t}):c.fraudServices&&c.fraudServices.useKount!==undefined&&n(c.fraudServices.useKount)?(c.pci||t.push("Missing pci property"),c.fraudServices.kount!==undefined&&c.fraudServices.kount.merchantId!==undefined&&c.fraudServices.kount.merchantId.trim()||t.push("Missing Kount MerchantId property"),c.cvvOnly&&t.push("Invalid property cvvOnly for fraudServices configuration"),i=t.length===0,{valid:i,details:t}):c.cvvOnly?(c.pci=!0,c.cardType||t.push("Missing cardType property"),c.token||t.push("Missing token property"),i=t.length===0,{valid:i,details:t}):(u=typeof c.customDataTypes!="undefined"&&c.customDataTypes!=="",c.cvv&&!c.cvvOnly)?(c.pci=!0,c.cvvContainerID||t.push("Missing cvvContainerID property"),u&&(ni()||t.push("Unknown custom data type for card type"),br()||t.push("Invalid card or cvv custom data type")),i=t.length===0,{valid:i,details:t}):c.forterSiteId||c.forterUsername?(c.pci||t.push("Missing pci property"),c.forterSiteId||t.push("Missing forterSiteId property"),c.forterUsername||t.push("Missing forterUsername property"),c.cvvOnly&&t.push("Invalid property cvvOnly for Forter configuration"),i=t.length===0,{valid:i,details:t}):c.pci&&!c.cvv&&!c.cvvOnly&&u?(ni()||t.push("Unknown custom data type for card type"),wr()||t.push("Invalid card custom data type"),i=t.length===0,{valid:i,details:t}):(i=t.length===0,{valid:i,details:t})}function ni(){var t,n;try{for(t=c.customDataTypes,n=0;n<t.length;n++)if(t[n].type.toLowerCase()==="unknown")return!1;return!0}catch(i){return!1}}function wr(){var t,n;try{for(t=c.customDataTypes,n=0;n<t.length;n++)if(!ti(t[n]))return!1;return!0}catch(i){return!1}}function br(){var t,n;try{for(t=c.customDataTypes,n=0;n<t.length;n++)if(!ti(t[n])||!kr(t[n]))return!1;return!0}catch(i){return!1}}function ti(n){return typeof n.type!="undefined"&&n.type!==""&&typeof n.validRegex!="undefined"&&n.validRegex!==""&&k(n.validRegex)&&typeof n.possibleRegEx!="undefined"&&n.possibleRegEx!==""&&k(n.possibleRegEx)&&typeof n.maxLength!="undefined"&&n.maxLength!==""&&parseInt(n.maxLength)>0}function kr(n){return typeof n.cvvValidRegex!="undefined"&&n.cvvValidRegex!==""&&k(n.cvvValidRegex)&&typeof n.cvvMaxLength!="undefined"&&n.cvvMaxLength!==""&&parseInt(n.cvvMaxLength)>=0}function k(n){var t=!0,i;try{i=new RegExp(n)}catch(r){t=!1}return t}function dr(){clearTimeout(u);var t=h(c.expiresInSeconds),n="ExpirationWarning";p({type:n,expirationTimeStamp:t});c.expiresInSeconds>60&&(u=setTimeout(function(){p({type:n,secondsRemaining:"60"})},e(c.expiresInSeconds-60)))}function gr(n,t){var f=[],r,u;return!n[0]||!n[0].threeDSMethodURL?(f.push("Device fingerprinting not supported for this PAN","Send 'MethodCompletionIndicator':'3' or 'MethodCompletionIndicator':'ResultUnavailable' within the ThreeDSecure Authentications request."),{details:f}):(r=i+"/iframe/v3/DeviceFingerprint",r+="?AuthenticationKey="+encodeURIComponent(c.authenticationKey),r+="&Origin="+encodeURIComponent(c.origin),r+="&TokenExID="+c.tokenExID,r+="&TokenScheme="+c.tokenScheme,r+="&PCI=true",r+="&Timestamp="+c.timestamp,r+="&CustomerRefNumber="+t,r+="&ThreeDSMethodUrl="+n[0].threeDSMethodURL,r+="&ThreeDSServerTransID="+n[0].threeDSServerTransID,r+="&ThreeDSMethodNotificationUrl="+c.threeDSMethodNotificationUrl,u=document.createElement("iframe"),u.setAttribute("name","tx_iframe_deviceFingerprint"),u.setAttribute("id","tx_iframe_deviceFingerprint"),u.setAttribute("style","display:none"),u.setAttribute("src",r),a.appendChild(u),{details:f})}function nu(){var n=i+"/iframe/v3/KountDataCollection",t;return n+="?AuthenticationKey="+encodeURIComponent(c.authenticationKey),n+="&TokenExID="+c.tokenExID,n+="&TokenScheme="+c.tokenScheme,n+="&Timestamp="+c.timestamp,n+="&MerchantId="+c.fraudServices.kount.merchantId,n+="&Origin="+encodeURIComponent(c.origin),n+="&Container="+a.id,t=document.createElement("iframe"),t.setAttribute("name","tx_kount_deviceDataCollector"),t.setAttribute("id","tx_kount_deviceDataCollector"),t.setAttribute("style","display:none"),t.setAttribute("src",n),a.appendChild(t),{details:[]}}function tu(n){return r(n)===null?{success:!1,details:"Error while parsing data, please verify and retry."}:(l({command:"setFraudServicesRiskRequestModel",data:n}),{success:!0,details:"Fraud Services risk request details have been received successfully."})}var a=document.getElementById(o),c=s,y;c.debug=n(c.debug);c.enablePrettyFormat=n(c.enablePrettyFormat);c.maskInput=n(c.maskInput);c.enableValidateOnBlur=n(c.enableValidateOnBlur);c.enableAriaRequired=n(c.enableAriaRequired);c.pci=n(c.pci);c.cvvOnly=n(c.cvvOnly);c.allowUnknownCardTypes=n(c.allowUnknownCardTypes);c.enableAutoComplete=n(c.enableAutoComplete);c.returnAutoCompleteValues=n(c.returnAutoCompleteValues);c.returnKhash=n(c.returnKhash);c.returnWhash=n(c.returnWhash);c.enforceLuhnCompliance=typeof c.enforceLuhnCompliance=="boolean"?Boolean(c.enforceLuhnCompliance):typeof c.enforceLuhnCompliance=="string"&&(c.enforceLuhnCompliance==="false"||c.enforceLuhnCompliance==="true")?n(c.enforceLuhnCompliance):!0;c.use3DS=n(c.use3DS);c.validateOnKeyUp=n(c.enableValidateOnKeyUp);c.validateOnCvvKeyUp=n(c.enableValidateOnCvvKeyUp);c.expiresInSeconds=parseInt(c.expiresInSeconds)>0&&parseInt(c.expiresInSeconds)<1200?parseInt(c.expiresInSeconds):1200;c.useExtendedBIN=n(c.useExtendedBIN);c.inlineIframeJavaScript=n(c.inlineIframeJavaScript);c.iframeVersion=isNaN(Number(c.iframeVersion))?null:c.iframeVersion;c.cvv&&(y=document.getElementById(c.cvvContainerID));var d=function(){},g=function(){},nt=function(){},tt=function(){},it=function(){},rt=function(){},ut=function(){},ft=function(){},et=function(){},ot=function(){},w=function(){},st=function(){},p=function(){},ht=function(){},ct=function(){},lt=function(){},at=function(){},vt=function(){},yt=function(){},pt=function(){},wt=function(){},bt=function(){};return{load:ui,on:ii,tokenize:ei,validate:oi,reset:li,blur:fr,cvvBlur:er,focus:nr,cvvFocus:tr,remove:ar,toggleMask:ir,toggleCvvMask:rr,setPAN:ur,binLookup:cr,validateConfig:ri,setFraudServicesRequestDetails:tu}}function s(u,f){function k(n,t){switch(n){case"load":a=t;break;case"error":v=t;break;case"expired":y=t}}function d(){var n,i;if(l(e),!ft()){e.debug=!0;l("Invalid Config Object");return}if(!t(o)){e.debug=!0;l("Invalid Container ID");return}n=document.getElementById("tx_iframe_"+o.id);window.addEventListener?addEventListener("message",c,!1):attachEvent("onmessage",c);t(n)||(n=document.createElement("iframe"),n.setAttribute("src",p(e.cvvOnly?"CVV":"Data")),n.setAttribute("frameborder","0"),n.setAttribute("width","100%"),n.setAttribute("height","100%"),n.setAttribute("id","tx_iframe_"+o.id),n.setAttribute("name","tx_iframe_"+o.id),o.appendChild(n));e.cvv&&!e.cvvOnly&&(i=document.getElementById("tx_iframe_cvv_"+s.id),t(i)||(i=document.createElement("iframe"),i.setAttribute("src",p("CVV")),i.setAttribute("frameborder","0"),i.setAttribute("width","100%"),i.setAttribute("height","100%"),i.setAttribute("id","tx_iframe_cvv_"+s.id),i.setAttribute("name","tx_iframe_cvv_"+s.id),s.appendChild(i)))}function p(n){var t=i+"/iframe/v3/detokenize?AuthenticationKey="+encodeURIComponent(e.authenticationKey)+"&Origin="+encodeURIComponent(e.origin)+"&TokenExID="+e.tokenExID+"&Timestamp="+e.timestamp+"&Container="+o.id+"&Token="+encodeURIComponent(e.token)+"&Mode="+n;return e.cvvOnly&&(t+="&CvvOnly=true"),e.cvv&&(t+="&CvvContainer="+e.cvvContainerID,t+="&CVV=true"),e.customDataLabel&&(t+="&CustomDataLabel="+e.customDataLabel),e.customCvvDataLabel&&(t+="&CustomCvvDataLabel="+e.customCvvDataLabel),e.title&&(t+="&Title="+e.title),e.expiresInSeconds&&(t+="&ExpiresInSeconds="+e.expiresInSeconds),t}function g(){e.styles&&nt(e.styles);e.enablePrettyFormat&&tt();e.debug&&ut();e.font&&rt(e.font)}function c(n){if(n&&n.data&&n.data!=="undefined"&&n.origin===i){var t=r(n.data);if(l(t),t&&t.container&&t.container===o.id)switch(t.event){case"load":g();a();break;case"error":v(t.data);break;case"expired":y();w()}}}function nt(n){h({command:"setStyle",data:n})}function tt(){h({command:"enablePrettyFormat"})}function it(){h({command:"reset"})}function h(n){var r=document.getElementById("tx_iframe_"+o.id);t(r)&&(n.fromIframeV3=!0,r.contentWindow.postMessage(JSON.stringify(n),i))}function w(){var r=document.getElementById("tx_iframe_"+o.id),n,i;t(r)&&r.parentNode.removeChild(r);s&&(n=document.getElementById("tx_iframe_cvv_"+s.id),t(n)&&n.parentNode.removeChild(n));e.use3DS&&(i=document.getElementById("tx_iframe_deviceFingerprint"),t(i)&&i.parentNode.removeChild(i));window.removeEventListener?removeEventListener("message",c,!1):detachEvent("onmessage",c)}function rt(n){h({command:"setFont",data:n})}function ut(){h({command:"enableDebug"})}function l(n){window.console&&e.debug&&console.log("TokenEx Iframe Debug: "+JSON.stringify(n))}function ft(){return e.cvvOnly?(e.pci=!0,e.origin&&e.tokenExID&&e.authenticationKey&&e.timestamp&&e.token):e.cvv&&!e.cvvOnly?(e.pci=!0,e.cvvContainerID):e.origin&&e.tokenExID&&e.authenticationKey&&e.timestamp?!0:!1}var o=document.getElementById(u),e=f,s,b=parseInt(e.expiresInSeconds)>60?60:20;e.cvvOnly=n(e.cvvOnly);e.enablePrettyFormat=n(e.enablePrettyFormat);e.expiresInSeconds=parseInt(e.expiresInSeconds)>0&&parseInt(e.expiresInSeconds)<=60?parseInt(e.expiresInSeconds):b;e.cvv&&(s=document.getElementById(e.cvvContainerID));var a=function(){},v=function(){},y=function(){};return{load:d,on:k,reset:it,remove:w}}function t(n){return typeof n=="undefined"||n===null?!1:!0}function r(n){try{return JSON.parse(n)}catch(t){return null}}function h(n){return new Date((new Date).getTime()+e(n)).toISOString().replace(/[-T:\.Z]/g,"").slice(0,-3)}function e(n){return n*1e3}function n(n){switch(typeof n){case"string":return n.toLowerCase()==="true";case"number":return n===1;case"boolean":return n;default:return!1}}var i="https://eu1-htp.tokenex.com",f=window.location.origin,u;return{Iframe:o,DetokenizeIframe:s}}();
