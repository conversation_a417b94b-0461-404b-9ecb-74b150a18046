<!DOCTYPE html>
<!-- saved from url=(0140)https://bh4f8ef25mffwh6g.canva-hosted-embed.com/codelet/AAEAEGJoNGY4ZWYyNW1mZndoNmcAAAAAAZhBgvfHdw2pVOhrmWYhJy5A-3pxnM9TIm1NHA0IxfY1uhScCx8/ -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Outfit Planner</title>
    <script src="./saved_resource"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        
        .outfit-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .outfit-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .ai-thinking {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .weather-icon {
            font-size: 2rem;
        }
        
        .clothing-item {
            transition: all 0.2s ease;
        }
        
        .clothing-item:hover {
            transform: scale(1.05);
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.mx-auto{margin-left:auto;margin-right:auto}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.mt-12{margin-top:3rem}.mt-2{margin-top:0.5rem}.mt-6{margin-top:1.5rem}.mr-2{margin-right:0.5rem}.block{display:block}.inline-block{display:inline-block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.min-h-screen{min-height:100vh}.w-full{width:100%}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.items-center{align-items:center}.justify-center{justify-content:center}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))}.bg-blue-600{--tw-bg-opacity:1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1))}.bg-green-600{--tw-bg-opacity:1;background-color:rgb(22 163 74 / var(--tw-bg-opacity, 1))}.bg-purple-600{--tw-bg-opacity:1;background-color:rgb(147 51 234 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-gradient-to-br{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops))}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.from-purple-50{--tw-gradient-from:#faf5ff var(--tw-gradient-from-position);--tw-gradient-to:rgb(250 245 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-purple-600{--tw-gradient-from:#9333ea var(--tw-gradient-from-position);--tw-gradient-to:rgb(147 51 234 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-blue-50{--tw-gradient-to:#eff6ff var(--tw-gradient-to-position)}.to-blue-600{--tw-gradient-to:#2563eb var(--tw-gradient-to-position)}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-8{padding-top:2rem;padding-bottom:2rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.opacity-90{opacity:0.9}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.hover\:bg-blue-700:hover{--tw-bg-opacity:1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.hover\:bg-green-700:hover{--tw-bg-opacity:1;background-color:rgb(21 128 61 / var(--tw-bg-opacity, 1))}.hover\:bg-purple-700:hover{--tw-bg-opacity:1;background-color:rgb(126 34 206 / var(--tw-bg-opacity, 1))}.hover\:from-purple-700:hover{--tw-gradient-from:#7e22ce var(--tw-gradient-from-position);--tw-gradient-to:rgb(126 34 206 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.hover\:to-blue-700:hover{--tw-gradient-to:#1d4ed8 var(--tw-gradient-to-position)}.focus\:border-transparent:focus{border-color:transparent}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-purple-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(168 85 247 / var(--tw-ring-opacity, 1))}@media (min-width: 768px){.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}}@media (min-width: 1024px){.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}</style></head>
<body class="bg-gradient-to-br from-purple-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">🤖 AI Outfit Planner</h1>
            <p class="text-gray-600">Let AI help you choose the perfect outfit for any occasion</p>
        </div>

        <!-- Input Panel -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div class="grid md:grid-cols-3 gap-6">
                <!-- Weather Input -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Weather</label>
                    <select id="weather" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <option value="sunny">☀️ Sunny (75°F)</option>
                        <option value="cloudy">☁️ Cloudy (65°F)</option>
                        <option value="rainy">🌧️ Rainy (55°F)</option>
                        <option value="cold">❄️ Cold (35°F)</option>
                        <option value="hot">🔥 Hot (85°F)</option>
                    </select>
                </div>

                <!-- Occasion Input -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Occasion</label>
                    <select id="occasion" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <option value="casual">👕 Casual</option>
                        <option value="work">💼 Work/Business</option>
                        <option value="formal">🎩 Formal Event</option>
                        <option value="date">💕 Date Night</option>
                        <option value="workout">🏃 Workout</option>
                        <option value="party">🎉 Party</option>
                    </select>
                </div>

                <!-- Style Preference -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Style</label>
                    <select id="style" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <option value="trendy">✨ Trendy</option>
                        <option value="classic">👔 Classic</option>
                        <option value="bohemian">🌸 Bohemian</option>
                        <option value="minimalist">⚪ Minimalist</option>
                        <option value="edgy">🖤 Edgy</option>
                        <option value="romantic">💖 Romantic</option>
                    </select>
                </div>
            </div>

            <button onclick="generateOutfit()" class="w-full mt-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105">
                🎯 Generate AI Outfit Recommendation
            </button>
        </div>

        <!-- AI Thinking Animation -->
        <div id="aiThinking" class="text-center mb-8 hidden">
            <div class="ai-thinking inline-block bg-white rounded-full p-4 shadow-lg">
                <div class="text-2xl">🤖</div>
            </div>
            <p class="mt-2 text-gray-600">AI is analyzing your preferences...</p>
        </div>

        <!-- Outfit Recommendation -->
        <div id="outfitResult" class="">
            <div class="outfit-card rounded-2xl p-8 text-white mb-8">
                <div class="text-center">
                    <h2 class="text-3xl font-bold mb-2">Your Perfect Outfit</h2>
                    <p id="outfitDescription" class="text-lg opacity-90">Perfect for a Casual Day in ☀️ sunny weather with trendy style</p>
                </div>
            </div>

            <!-- Outfit Items -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div id="topItem" class="clothing-item bg-white rounded-xl p-6 shadow-lg text-center">
                    <div class="text-4xl mb-3">👕</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Top</h3>
                    <p id="topDesc" class="text-gray-600 text-sm">Crop top</p>
                </div>

                <div id="bottomItem" class="clothing-item bg-white rounded-xl p-6 shadow-lg text-center">
                    <div class="text-4xl mb-3">👖</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Bottom</h3>
                    <p id="bottomDesc" class="text-gray-600 text-sm">High-waisted shorts</p>
                </div>

                <div id="shoesItem" class="clothing-item bg-white rounded-xl p-6 shadow-lg text-center">
                    <div class="text-4xl mb-3">👟</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Shoes</h3>
                    <p id="shoesDesc" class="text-gray-600 text-sm">White sneakers</p>
                </div>

                <div id="accessoryItem" class="clothing-item bg-white rounded-xl p-6 shadow-lg text-center">
                    <div class="text-4xl mb-3">👜</div>
                    <h3 class="font-semibold text-gray-800 mb-2">Accessory</h3>
                    <p id="accessoryDesc" class="text-gray-600 text-sm">Sunglasses</p>
                </div>
            </div>

            <!-- AI Tips -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🎯 AI Styling Tips</h3>
                <div id="stylingTips" class="space-y-2"><div class="flex items-center text-gray-700"><span class="text-purple-500 mr-2">•</span> Layer with a light cardigan</div><div class="flex items-center text-gray-700"><span class="text-purple-500 mr-2">•</span> Add colorful accessories</div><div class="flex items-center text-gray-700"><span class="text-purple-500 mr-2">•</span> Choose breathable fabrics</div></div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-4 mt-6 justify-center">
                <button onclick="generateOutfit()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    🔄 Generate New Outfit
                </button>
                <button onclick="saveOutfit()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                    💾 Save to Favorites
                </button>
                <button onclick="shareOutfit()" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                    📤 Share Outfit
                </button>
            </div>
        </div>

        <!-- Saved Outfits -->
        <div id="savedOutfits" class="mt-12">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">💾 Your Saved Outfits</h3>
            <div id="savedOutfitsList" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
        </div>
    </div>

    <script>
        // AI Outfit Database
        const outfitDatabase = {
            sunny: {
                casual: {
                    trendy: { top: "Crop top", bottom: "High-waisted shorts", shoes: "White sneakers", accessory: "Sunglasses", tips: ["Layer with a light cardigan", "Add colorful accessories", "Choose breathable fabrics"] },
                    classic: { top: "Cotton t-shirt", bottom: "Denim jeans", shoes: "Canvas sneakers", accessory: "Baseball cap", tips: ["Stick to neutral colors", "Ensure proper fit", "Add a watch for sophistication"] }
                },
                work: {
                    classic: { top: "Blouse", bottom: "Dress pants", shoes: "Loafers", accessory: "Structured bag", tips: ["Choose wrinkle-resistant fabrics", "Keep colors professional", "Ensure comfort for long days"] }
                }
            },
            rainy: {
                casual: {
                    trendy: { top: "Hoodie", bottom: "Joggers", shoes: "Waterproof boots", accessory: "Umbrella", tips: ["Layer for warmth", "Choose quick-dry materials", "Don't forget a rain jacket"] }
                }
            },
            cold: {
                casual: {
                    trendy: { top: "Sweater", bottom: "Warm leggings", shoes: "Boots", accessory: "Scarf", tips: ["Layer effectively", "Choose warm materials", "Don't sacrifice style for warmth"] }
                }
            }
        };

        let savedOutfitsArray = JSON.parse(localStorage.getItem('savedOutfits')) || [];

        function generateOutfit() {
            const weather = document.getElementById('weather').value;
            const occasion = document.getElementById('occasion').value;
            const style = document.getElementById('style').value;

            // Show AI thinking animation
            document.getElementById('aiThinking').classList.remove('hidden');
            document.getElementById('outfitResult').classList.add('hidden');

            setTimeout(() => {
                // Hide AI thinking
                document.getElementById('aiThinking').classList.add('hidden');
                
                // Generate outfit based on AI logic
                const outfit = getAIOutfitRecommendation(weather, occasion, style);
                
                // Display outfit
                displayOutfit(outfit, weather, occasion, style);
                
                // Show result
                document.getElementById('outfitResult').classList.remove('hidden');
            }, 2000);
        }

        function getAIOutfitRecommendation(weather, occasion, style) {
            // AI logic to determine outfit
            const baseOutfit = outfitDatabase[weather]?.[occasion]?.[style] || 
                              outfitDatabase[weather]?.casual?.trendy ||
                              { top: "T-shirt", bottom: "Jeans", shoes: "Sneakers", accessory: "Watch", tips: ["Mix and match colors", "Ensure comfort", "Express your personality"] };

            // Add weather-specific modifications
            let outfit = { ...baseOutfit };
            
            if (weather === 'hot') {
                outfit = { top: "Tank top", bottom: "Shorts", shoes: "Sandals", accessory: "Sun hat", tips: ["Choose light colors", "Prioritize breathability", "Stay hydrated"] };
            } else if (weather === 'cold') {
                outfit = { top: "Warm sweater", bottom: "Thermal leggings", shoes: "Winter boots", accessory: "Warm scarf", tips: ["Layer effectively", "Choose insulating materials", "Don't forget gloves"] };
            }

            // Style modifications
            if (style === 'minimalist') {
                outfit.tips.push("Stick to neutral colors", "Choose clean lines", "Less is more");
            } else if (style === 'edgy') {
                outfit.tips.push("Add leather elements", "Try bold accessories", "Mix textures");
            }

            return outfit;
        }

        function displayOutfit(outfit, weather, occasion, style) {
            const weatherEmojis = { sunny: '☀️', cloudy: '☁️', rainy: '🌧️', cold: '❄️', hot: '🔥' };
            const occasionText = { casual: 'Casual Day', work: 'Work Day', formal: 'Formal Event', date: 'Date Night', workout: 'Workout', party: 'Party Time' };
            
            document.getElementById('outfitDescription').textContent = 
                `Perfect for a ${occasionText[occasion]} in ${weatherEmojis[weather]} ${weather} weather with ${style} style`;
            
            document.getElementById('topDesc').textContent = outfit.top;
            document.getElementById('bottomDesc').textContent = outfit.bottom;
            document.getElementById('shoesDesc').textContent = outfit.shoes;
            document.getElementById('accessoryDesc').textContent = outfit.accessory;
            
            const tipsContainer = document.getElementById('stylingTips');
            tipsContainer.innerHTML = '';
            outfit.tips.forEach(tip => {
                const tipElement = document.createElement('div');
                tipElement.className = 'flex items-center text-gray-700';
                tipElement.innerHTML = `<span class="text-purple-500 mr-2">•</span> ${tip}`;
                tipsContainer.appendChild(tipElement);
            });
        }

        function saveOutfit() {
            const weather = document.getElementById('weather').value;
            const occasion = document.getElementById('occasion').value;
            const style = document.getElementById('style').value;
            
            const outfit = {
                id: Date.now(),
                weather,
                occasion,
                style,
                top: document.getElementById('topDesc').textContent,
                bottom: document.getElementById('bottomDesc').textContent,
                shoes: document.getElementById('shoesDesc').textContent,
                accessory: document.getElementById('accessoryDesc').textContent,
                date: new Date().toLocaleDateString()
            };

            savedOutfitsArray.push(outfit);
            localStorage.setItem('savedOutfits', JSON.stringify(savedOutfitsArray));
            
            alert('✅ Outfit saved to your favorites!');
            displaySavedOutfits();
        }

        function shareOutfit() {
            const outfitText = `Check out my AI-generated outfit: ${document.getElementById('topDesc').textContent}, ${document.getElementById('bottomDesc').textContent}, ${document.getElementById('shoesDesc').textContent}, and ${document.getElementById('accessoryDesc').textContent}!`;
            
            if (navigator.share) {
                navigator.share({
                    title: 'My AI Outfit',
                    text: outfitText
                });
            } else {
                navigator.clipboard.writeText(outfitText);
                alert('📋 Outfit details copied to clipboard!');
            }
        }

        function displaySavedOutfits() {
            const container = document.getElementById('savedOutfitsList');
            container.innerHTML = '';
            
            savedOutfitsArray.slice(-6).reverse().forEach(outfit => {
                const outfitCard = document.createElement('div');
                outfitCard.className = 'bg-white rounded-xl p-4 shadow-lg';
                outfitCard.innerHTML = `
                    <div class="text-sm text-gray-500 mb-2">${outfit.date}</div>
                    <div class="font-semibold text-gray-800 mb-2">${outfit.occasion} • ${outfit.style}</div>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div>👕 ${outfit.top}</div>
                        <div>👖 ${outfit.bottom}</div>
                        <div>👟 ${outfit.shoes}</div>
                        <div>👜 ${outfit.accessory}</div>
                    </div>
                    <button onclick="deleteOutfit(${outfit.id})" class="mt-3 text-red-500 text-sm hover:text-red-700">
                        🗑️ Delete
                    </button>
                `;
                container.appendChild(outfitCard);
            });
        }

        function deleteOutfit(id) {
            savedOutfitsArray = savedOutfitsArray.filter(outfit => outfit.id !== id);
            localStorage.setItem('savedOutfits', JSON.stringify(savedOutfitsArray));
            displaySavedOutfits();
        }

        // Initialize saved outfits display
        displaySavedOutfits();

        // Generate initial outfit
        generateOutfit();
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'964358b83104dc68',t:'MTc1MzM1OTM2NC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;" src="./saved_resource.html"></iframe>

</body></html>